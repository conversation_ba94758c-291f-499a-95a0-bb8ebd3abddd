# Generated by Django 4.2.7 on 2025-06-06 15:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_accounts', '0011_student_signature'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='hod_signing',
            name='student',
        ),
        migrations.RemoveField(
            model_name='hostel_signing',
            name='student',
        ),
        migrations.RemoveField(
            model_name='library_signing',
            name='student',
        ),
        migrations.RemoveField(
            model_name='sport_director_signing',
            name='student',
        ),
        migrations.RemoveField(
            model_name='student_affair_signing',
            name='student',
        ),
        migrations.RemoveField(
            model_name='sug_signing',
            name='student',
        ),
        migrations.AlterField(
            model_name='customuser',
            name='position',
            field=models.CharField(choices=[('student', 'Student'), ('sug', 'SUG'), ('sport_director', 'Sport Director'), ('exams_records', 'Exams and Records'), ('hostel', 'Hostel'), ('library', 'Library'), ('student_affair', 'Student Affairs'), ('hod', 'HOD')], default='student', help_text="User's position in the institution", max_length=20),
        ),
        migrations.DeleteModel(
            name='Exam_and_record_signing',
        ),
        migrations.DeleteModel(
            name='Hod_signing',
        ),
        migrations.DeleteModel(
            name='Hostel_signing',
        ),
        migrations.DeleteModel(
            name='Library_signing',
        ),
        migrations.DeleteModel(
            name='Sport_director_signing',
        ),
        migrations.DeleteModel(
            name='Student_affair_signing',
        ),
        migrations.DeleteModel(
            name='Sug_signing',
        ),
    ]
