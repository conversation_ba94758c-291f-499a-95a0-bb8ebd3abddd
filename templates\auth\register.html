{% extends 'auth/base.html' %}

{% load static  %}

{% block content %}

  <body>
    <div class="container-fluid p-0">
      <div class="row m-0">
        <div class="col-12 p-0">    
          <div class="login-card login-dark">
            <div>
              <div class="login-main"> 
                <form class="theme-form" method="post" action="" id="registerForm">
                  {% csrf_token %}
                  <h2 class="text-center">Clearance Application</h2>
                  <p class="text-center">Enter your personal details to create account</p>
                  <div id="step-1" class="register-step">
                    <div class="form-group">
                      <label class="col-form-label pt-0">Fullname</label>
                      <div class="row g-2">
                        <div class="col-12">
                          <input class="form-control" type="text" name="fullname" required placeholder="Fullname">
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-form-label">Mat No</label>
                      <input class="form-control" type="text" name="mat_no" required placeholder="Matric Number">
                    </div>
                    <div class="form-group">
                      <label class="col-form-label">Email Address</label>
                      <input class="form-control" type="email" name="email" required placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                      <label class="col-form-label">Password</label>
                      <div class="form-input position-relative">
                        <input class="form-control" type="password" name="password" required placeholder="*********">
                        <div class="show-hide"><span class="show"></span></div>
                      </div>
                    </div>
                    <div class="form-group mb-0 checkbox-checked text-end">
                      <button type="button" class="btn btn-primary mt-3" id="nextBtn">Next</button>
                    </div>
                  </div>
                  <div id="step-2" class="register-step" style="display:none;">
                    <div class="form-group">
                      <label class="col-form-label">Department</label>
                      <select class="form-control" name="department" required>
                        <option value="" disabled selected>Select Department</option>
                        {% for dept in departments %}
                          <option value="{{ dept.id }}">{{ dept.title }}</option>
                        {% endfor %}
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="col-form-label">Level</label>
                      <select class="form-control" name="level" required>
                        <option value="" disabled selected>Select Level</option>
                        {% for level in levels %}
                          <option value="{{ level }}">{{ level }}</option>
                        {% endfor %}
                      </select>
                    </div>
                    <div class="form-group mb-0 checkbox-checked d-flex justify-content-between">
                      <button type="button" class="btn btn-secondary mt-3" id="backBtn">Back</button>
                      <button class="btn btn-primary mt-3" type="submit">Create Account</button>
                    </div>
                    <p class="mt-4 mb-0 text-center">Already have an account?<a class="ms-2" href="{% url 'login' %}">Sign in</a></p>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
      <script>
        document.getElementById('nextBtn').onclick = function() {
          document.getElementById('step-1').style.display = 'none';
          document.getElementById('step-2').style.display = 'block';
        };
        document.getElementById('backBtn').onclick = function() {
          document.getElementById('step-2').style.display = 'none';
          document.getElementById('step-1').style.display = 'block';
        };
      </script>
    {% endblock %}