# Generated by Django 4.2.7 on 2025-06-19 08:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user_accounts', '0019_announcement'),
    ]

    operations = [
        migrations.CreateModel(
            name='AcademicSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=20, unique=True)),
            ],
        ),
        migrations.AddField(
            model_name='statement_result',
            name='department',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.department'),
        ),
        migrations.AddField(
            model_name='statement_result',
            name='grade',
            field=models.CharField(choices=[('Distinction', 'Distinction'), ('Upper Credit', 'Upper Credit'), ('Lower Credit', 'Lower Credit'), ('Pass', 'Pass')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='statement_result',
            name='programme',
            field=models.CharField(choices=[('National Diploma', 'National Diploma'), ('Higher National Diploma', 'Higher National Diploma')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='statement_result',
            name='academic_session',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='user_accounts.academicsession'),
        ),
    ]
