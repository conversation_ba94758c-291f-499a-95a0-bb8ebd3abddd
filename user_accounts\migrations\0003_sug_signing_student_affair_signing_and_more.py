# Generated by Django 4.2.7 on 2025-03-27 15:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user_accounts', '0002_remove_customuser_staff'),
    ]

    operations = [
        migrations.CreateModel(
            name='Sug_signing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('departmental_due_receipt', models.FileField(upload_to='departmental_due_receipts/')),
                ('nacos_due_receipt', models.FileField(upload_to='departmental_due_receipts/')),
                ('approved', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('date_signed', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student')),
            ],
        ),
        migrations.CreateModel(
            name='Student_affair_signing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('school_fee_receipt', models.FileField(upload_to='school_fee_receipts/')),
                ('approved', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('date_signed', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student')),
            ],
        ),
        migrations.CreateModel(
            name='Sport_director_signing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('departmental_due_receipt', models.FileField(upload_to='departmental_due_receipts/')),
                ('nacos_due_receipt', models.FileField(upload_to='departmental_due_receipts/')),
                ('school_fee_receipt', models.FileField(upload_to='school_fee_receipts/')),
                ('approved', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('date_signed', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student')),
            ],
        ),
        migrations.CreateModel(
            name='Library_signing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('approved', models.BooleanField(default=False)),
                ('school_fee_receipt', models.FileField(upload_to='school_fee_receipts/')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('date_signed', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student')),
            ],
        ),
        migrations.CreateModel(
            name='Hostel_signing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('school_fee_receipt', models.FileField(upload_to='school_fee_receipts/')),
                ('approved', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('date_signed', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student')),
            ],
        ),
        migrations.CreateModel(
            name='Hod_signing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('departmental_due_receipt', models.FileField(upload_to='departmental_due_receipts/')),
                ('nacos_due_receipt', models.FileField(upload_to='departmental_due_receipts/')),
                ('approved', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('date_signed', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student')),
            ],
        ),
        migrations.CreateModel(
            name='Exam_and_record_signing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('waec_result', models.FileField(upload_to='waec_results/')),
                ('approved', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('date_signed', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student')),
            ],
        ),
    ]
