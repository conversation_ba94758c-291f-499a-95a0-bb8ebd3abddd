# Generated by Django 4.2.7 on 2025-04-14 15:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user_accounts', '0003_sug_signing_student_affair_signing_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='exam_and_record_signing',
            name='user',
        ),
        migrations.RemoveField(
            model_name='hod_signing',
            name='user',
        ),
        migrations.RemoveField(
            model_name='hostel_signing',
            name='user',
        ),
        migrations.RemoveField(
            model_name='library_signing',
            name='user',
        ),
        migrations.RemoveField(
            model_name='sport_director_signing',
            name='user',
        ),
        migrations.RemoveField(
            model_name='student_affair_signing',
            name='user',
        ),
        migrations.RemoveField(
            model_name='sug_signing',
            name='user',
        ),
        migrations.AddField(
            model_name='exam_and_record_signing',
            name='student',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student'),
        ),
        migrations.AddField(
            model_name='hod_signing',
            name='student',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student'),
        ),
        migrations.AddField(
            model_name='hostel_signing',
            name='student',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student'),
        ),
        migrations.AddField(
            model_name='library_signing',
            name='student',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student'),
        ),
        migrations.AddField(
            model_name='sport_director_signing',
            name='student',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student'),
        ),
        migrations.AddField(
            model_name='student',
            name='department',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='departmental_due_receipt',
            field=models.FileField(null=True, upload_to='departmental_due_receipts/'),
        ),
        migrations.AddField(
            model_name='student',
            name='level',
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='student',
            name='mat_no',
            field=models.CharField(max_length=20, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='student',
            name='nacos_due_receipt',
            field=models.FileField(null=True, upload_to='nacos_due_receipts/'),
        ),
        migrations.AddField(
            model_name='student',
            name='school_fee_receipt',
            field=models.FileField(null=True, upload_to='school_fee_receipts/'),
        ),
        migrations.AddField(
            model_name='student',
            name='waec_result',
            field=models.FileField(null=True, upload_to='waec_results/'),
        ),
        migrations.AddField(
            model_name='student_affair_signing',
            name='student',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student'),
        ),
        migrations.AddField(
            model_name='sug_signing',
            name='student',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_accounts.student'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='position',
            field=models.CharField(choices=[('student', 'Student'), ('sug', 'SUG'), ('sport_director', 'Sport Director'), ('exams_records', 'Exams and Records'), ('hostel', 'Hostel'), ('library', 'Library'), ('student_affair', 'Student Affairs'), ('hod', 'HOD'), ('exam_and_record', 'Exam and Record')], default='student', help_text="User's position in the institution", max_length=20),
        ),
        migrations.AlterField(
            model_name='exam_and_record',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='exam_and_record_signing',
            name='waec_result',
            field=models.FileField(null=True, upload_to='waec_results/'),
        ),
        migrations.AlterField(
            model_name='hod',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='hod_signing',
            name='departmental_due_receipt',
            field=models.FileField(null=True, upload_to='departmental_due_receipts/'),
        ),
        migrations.AlterField(
            model_name='hod_signing',
            name='nacos_due_receipt',
            field=models.FileField(null=True, upload_to='departmental_due_receipts/'),
        ),
        migrations.AlterField(
            model_name='hostel',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='hostel_signing',
            name='school_fee_receipt',
            field=models.FileField(null=True, upload_to='school_fee_receipts/'),
        ),
        migrations.AlterField(
            model_name='library',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='library_signing',
            name='school_fee_receipt',
            field=models.FileField(null=True, upload_to='school_fee_receipts/'),
        ),
        migrations.AlterField(
            model_name='sport_director',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='sport_director_signing',
            name='departmental_due_receipt',
            field=models.FileField(null=True, upload_to='departmental_due_receipts/'),
        ),
        migrations.AlterField(
            model_name='sport_director_signing',
            name='nacos_due_receipt',
            field=models.FileField(null=True, upload_to='departmental_due_receipts/'),
        ),
        migrations.AlterField(
            model_name='sport_director_signing',
            name='school_fee_receipt',
            field=models.FileField(null=True, upload_to='school_fee_receipts/'),
        ),
        migrations.AlterField(
            model_name='student_affair',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='student_affair_signing',
            name='school_fee_receipt',
            field=models.FileField(null=True, upload_to='school_fee_receipts/'),
        ),
        migrations.AlterField(
            model_name='sug',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='sug_signing',
            name='departmental_due_receipt',
            field=models.FileField(null=True, upload_to='departmental_due_receipts/'),
        ),
        migrations.AlterField(
            model_name='sug_signing',
            name='nacos_due_receipt',
            field=models.FileField(null=True, upload_to='departmental_due_receipts/'),
        ),
    ]
