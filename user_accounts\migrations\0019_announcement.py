# Generated by Django 4.2.7 on 2025-06-18 16:52

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_accounts', '0018_statement_result_issued_by_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('sender_position', models.CharField(max_length=100)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('recipients', models.ManyToManyField(related_name='announcements', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
