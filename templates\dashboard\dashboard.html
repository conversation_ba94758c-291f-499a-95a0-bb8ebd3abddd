{% extends 'base.html' %}
{% load static %}
{% block content %}
<style>
.clearance-card {
  transition: all 0.3s ease;
  border-radius: 15px;
  overflow: hidden;
}

.clearance-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.clearance-card.border-success {
  border-left: 5px solid #28a745 !important;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.1) 100%);
}

.clearance-card .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.clearance-card.border-success .card-header {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
}

.overall-status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
}

.overall-status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.overall-status-card .card-body {
  position: relative;
  z-index: 2;
}

.overall-status-card .text-success {
  color: #28a745 !important;
}

.overall-status-card .text-warning {
  color: #ffc107 !important;
}

.overall-status-card .text-info {
  color: #17a2b8 !important;
}

.badge {
  font-size: 0.75rem;
  padding: 0.5em 0.75em;
}

.feedback-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.department-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #f8f9fa;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 2px;
}

.department-icon:hover {
  border-color: #007bff;
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(0,123,255,0.3);
}

.clearance-card .icon {
  position: relative;
}

.clearance-card .icon::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(135deg, rgba(0,123,255,0.1) 0%, rgba(108,117,125,0.1) 100%);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.clearance-card:hover .icon::before {
  opacity: 1;
}

.status-approved {
  background: linear-gradient(45deg, #28a745, #20c997);
  color: white;
}

.status-pending {
  background: linear-gradient(45deg, #ffc107, #fd7e14);
  color: white;
}

.status-rejected {
  background: linear-gradient(45deg, #dc3545, #e83e8c);
  color: white;
}

.staff-welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.staff-welcome-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.staff-welcome-card .card-body {
  position: relative;
  z-index: 2;
}

.staff-welcome-card .btn-light {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  transition: all 0.3s ease;
}

.staff-welcome-card .btn-light:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}
</style>
<div class="page-body">
  <div class="container-fluid">
    <div class="page-title">
      <div class="row">
        <div class="col-sm-6 col-12">
          <h2>Student Clearance Dashboard</h2>
          <p class="mb-0 text-title-gray">Monitor and complete your clearance steps below.</p>
        </div>
      </div>
    </div>
  </div>

  <div class="container-fluid dashboard-3">
    <!-- Overall Clearance Status -->
    {% comment %} {% if user.position == "student" %}
      <div class="row mb-4">
        <div class="col-12">
          <div class="card border-0 shadow-sm overall-status-card">
            <div class="card-body text-center py-4">
              {% if overall_status == "Completely Approved" %}
                <div class="text-success mb-3">
                  <i class="fas fa-check-circle fa-3x"></i>
                </div>
                <h3 class="text-success mb-2">Clearance Complete!</h3>
                <p class="text-muted">All departments have approved your clearance. You can now proceed to collect your documents.</p>
              {% elif overall_status == "In Progress" %}
                <div class="text-warning mb-3">
                  <i class="fas fa-clock fa-3x"></i>
                </div>
                <h3 class="text-warning mb-2">Clearance In Progress</h3>
                <p class="text-muted">Some departments are still reviewing your documents. Please check individual department status below.</p>
              {% else %}
                <div class="text-info mb-3">
                  <i class="fas fa-upload fa-3x"></i>
                </div>
                <h3 class="text-info mb-2">Upload Documents</h3>
                <p class="text-muted">Please upload your documents to begin the clearance process.</p>
                <a href="{% url 'upload_doc' %}" class="btn btn-primary">Upload Documents</a>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    {% endif %} {% endcomment %}

    <div class="row">
      {% if user.position == "student" %}
        <!-- HOD -->
        <div class="col-xxl-4 col-xl-4 col-md-6">
          <div class="card clearance-card graphic-design overflow-hidden {% if clearance_data.hod.status == 'approved' %}border-success{% endif %}">
            <div class="card-header card-no-border pb-0">
              <div class="header-top">
                <div class="d-flex align-items-center gap-2">
                  <div class="flex-shrink-0">
                    <div class="icon">
                      {% if clearance_data.hod.image %}
                        <img class="department-icon" src="{{ clearance_data.hod.image }}" alt="HOD" width="50">
                      {% else %}
                        <img class="department-icon" src="{% static clearance_data.hod.default_image %}" alt="HOD" width="50">
                      {% endif %}
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5>{{ clearance_data.hod.name }}</h5>
                    <p class="mb-0 text-muted">{{ clearance_data.hod.staff_name }}</p>
                  </div>
                </div>
                {% if clearance_data.hod.status == 'approved' %}
                  <span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>
                {% else %}
                  <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                {% endif %}
              </div>
            </div>
            <div class="card-body p-3">
              <p>Complete your department clearance here.</p>
              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'view_clearance_detail' 'hod' %}" class="btn btn-outline-primary btn-sm">
                  <i class="fas fa-eye"></i> View Clearance
                </a>
                {% if clearance_data.hod.feedback %}
                  <span class="badge bg-info feedback-indicator">
                    <i class="fas fa-comment"></i> {{ clearance_data.hod.feedback|length }} feedback(s)
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- SUG -->
        <div class="col-xxl-4 col-xl-4 col-md-6">
          <div class="card clearance-card graphic-design overflow-hidden {% if clearance_data.sug.status == 'approved' %}border-success{% endif %}">
            <div class="card-header card-no-border pb-0">
              <div class="header-top">
                <div class="d-flex align-items-center gap-2">
                  <div class="flex-shrink-0">
                    <div class="icon">
                      {% if clearance_data.sug.image %}
                        <img class="department-icon" src="{{ clearance_data.sug.image }}" alt="SUG" width="50">
                      {% else %}
                        <img class="department-icon" src="{% static clearance_data.sug.default_image %}" alt="SUG" width="50">
                      {% endif %}
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5>{{ clearance_data.sug.name }}</h5>
                    <p class="mb-0 text-muted">{{ clearance_data.sug.staff_name }}</p>
                  </div>
                </div>
                {% if clearance_data.sug.status == 'approved' %}
                  <span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>
                {% else %}
                  <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                {% endif %}
              </div>
            </div>
            <div class="card-body p-3">
              <p>Get your SUG clearance signed off.</p>
              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'view_clearance_detail' 'sug' %}" class="btn btn-outline-primary btn-sm">
                  <i class="fas fa-eye"></i> View Clearance
                </a>
                {% if clearance_data.sug.feedback %}
                  <span class="badge bg-info feedback-indicator">
                    <i class="fas fa-comment"></i> {{ clearance_data.sug.feedback|length }} feedback(s)
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Library -->
        <div class="col-xxl-4 col-xl-4 col-md-6">
          <div class="card clearance-card graphic-design overflow-hidden {% if clearance_data.library.status == 'approved' %}border-success{% endif %}">
            <div class="card-header card-no-border pb-0">
              <div class="header-top">
                <div class="d-flex align-items-center gap-2">
                  <div class="flex-shrink-0">
                    <div class="icon">
                      {% if clearance_data.library.image %}
                        <img class="department-icon" src="{{ clearance_data.library.image }}" alt="Library" width="50">
                      {% else %}
                        <img class="department-icon" src="{% static clearance_data.library.default_image %}" alt="Library" width="50">
                      {% endif %}
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5>{{ clearance_data.library.name }}</h5>
                    <p class="mb-0 text-muted">{{ clearance_data.library.staff_name }}</p>
                  </div>
                </div>
                {% if clearance_data.library.status == 'approved' %}
                  <span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>
                {% else %}
                  <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                {% endif %}
              </div>
            </div>
            <div class="card-body p-3">
              <p>Ensure all borrowed books are returned.</p>
              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'view_clearance_detail' 'library' %}" class="btn btn-outline-primary btn-sm">
                  <i class="fas fa-eye"></i> View Clearance
                </a>
                {% if clearance_data.library.feedback %}
                  <span class="badge bg-info feedback-indicator">
                    <i class="fas fa-comment"></i> {{ clearance_data.library.feedback|length }} feedback(s)
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Sport Director -->
        <div class="col-xxl-4 col-xl-4 col-md-6">
          <div class="card clearance-card graphic-design overflow-hidden {% if clearance_data.sport_director.status == 'approved' %}border-success{% endif %}">
            <div class="card-header card-no-border pb-0">
              <div class="header-top">
                <div class="d-flex align-items-center gap-2">
                  <div class="flex-shrink-0">
                    <div class="icon">
                      {% if clearance_data.sport_director.image %}
                        <img class="department-icon" src="{{ clearance_data.sport_director.image }}" alt="Sport Director" width="50">
                      {% else %}
                        <img class="department-icon" src="{% static clearance_data.sport_director.default_image %}" alt="Sport Director" width="50">
                      {% endif %}
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5>{{ clearance_data.sport_director.name }}</h5>
                    <p class="mb-0 text-muted">{{ clearance_data.sport_director.staff_name }}</p>
                  </div>
                </div>
                {% if clearance_data.sport_director.status == 'approved' %}
                  <span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>
                {% else %}
                  <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                {% endif %}
              </div>
            </div>
            <div class="card-body p-3">
              <p>Confirm all sport dues and requirements.</p>
              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'view_clearance_detail' 'sport_director' %}" class="btn btn-outline-primary btn-sm">
                  <i class="fas fa-eye"></i> View Clearance
                </a>
                {% if clearance_data.sport_director.feedback %}
                  <span class="badge bg-info feedback-indicator">
                    <i class="fas fa-comment"></i> {{ clearance_data.sport_director.feedback|length }} feedback(s)
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Student Affairs -->
        <div class="col-xxl-4 col-xl-4 col-md-6">
          <div class="card clearance-card graphic-design overflow-hidden {% if clearance_data.student_affair.status == 'approved' %}border-success{% endif %}">
            <div class="card-header card-no-border pb-0">
              <div class="header-top">
                <div class="d-flex align-items-center gap-2">
                  <div class="flex-shrink-0">
                    <div class="icon">
                      <img class="department-icon" src="{% static clearance_data.student_affair.image %}" alt="Student Affairs" width="50">
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5>{{ clearance_data.student_affair.name }}</h5>
                    <p class="mb-0 text-muted">{{ clearance_data.student_affair.staff_name }}</p>
                  </div>
                </div>
                {% if clearance_data.student_affair.status == 'approved' %}
                  <span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>
                {% else %}
                  <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                {% endif %}
              </div>
            </div>
            <div class="card-body p-3">
              <p>Meet with student affairs for final clearance.</p>
              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'view_clearance_detail' 'student_affair' %}" class="btn btn-outline-primary btn-sm">
                  <i class="fas fa-eye"></i> View Clearance
                </a>
                {% if clearance_data.student_affair.feedback %}
                  <span class="badge bg-info feedback-indicator">
                    <i class="fas fa-comment"></i> {{ clearance_data.student_affair.feedback|length }} feedback(s)
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Exam & Records -->
        <div class="col-xxl-4 col-xl-4 col-md-6">
          <div class="card clearance-card graphic-design overflow-hidden {% if clearance_data.exam_record.status == 'approved' %}border-success{% endif %}">
            <div class="card-header card-no-border pb-0">
              <div class="header-top">
                <div class="d-flex align-items-center gap-2">
                  <div class="flex-shrink-0">
                    <div class="icon">
                      <img class="department-icon" src="{% static clearance_data.exam_record.image %}" alt="Exam & Records" width="50">
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5>{{ clearance_data.exam_record.name }}</h5>
                    <p class="mb-0 text-muted">{{ clearance_data.exam_record.staff_name }}</p>
                  </div>
                </div>
                {% if clearance_data.exam_record.status == 'approved' %}
                  <span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>
                {% else %}
                  <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                {% endif %}
              </div>
            </div>
            <div class="card-body p-3">
              <p>Ensure all results and records are cleared.</p>
              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'view_clearance_detail' 'exam_record' %}" class="btn btn-outline-primary btn-sm">
                  <i class="fas fa-eye"></i> View Clearance
                </a>
                {% if clearance_data.exam_record.feedback %}
                  <span class="badge bg-info feedback-indicator">
                    <i class="fas fa-comment"></i> {{ clearance_data.exam_record.feedback|length }} feedback(s)
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Hostel -->
        <div class="col-xxl-4 col-xl-4 col-md-6">
          <div class="card clearance-card graphic-design overflow-hidden {% if clearance_data.hostel.status == 'approved' %}border-success{% endif %}">
            <div class="card-header card-no-border pb-0">
              <div class="header-top">
                <div class="d-flex align-items-center gap-2">
                  <div class="flex-shrink-0">
                    <div class="icon">
                      <img class="department-icon" src="{% static clearance_data.hostel.image %}" alt="Hostel" width="50">
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5>{{ clearance_data.hostel.name }}</h5>
                    <p class="mb-0 text-muted">{{ clearance_data.hostel.staff_name }}</p>
                  </div>
                </div>
                {% if clearance_data.hostel.status == 'approved' %}
                  <span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>
                {% else %}
                  <span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>
                {% endif %}
              </div>
            </div>
            <div class="card-body p-3">
              <p>Clear all hostel obligations.</p>
              <div class="d-flex justify-content-between align-items-center">
                <a href="{% url 'view_clearance_detail' 'hostel' %}" class="btn btn-outline-primary btn-sm">
                  <i class="fas fa-eye"></i> View Clearance
                </a>
                {% if clearance_data.hostel.feedback %}
                  <span class="badge bg-info feedback-indicator">
                    <i class="fas fa-comment"></i> {{ clearance_data.hostel.feedback|length }} feedback(s)
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      {% else %}
        <!-- Staff Dashboard -->
        <div class="col-12">
          <div class="card border-0 shadow-lg staff-welcome-card" styl>
            <div class="card-body p-5 text-center">
              <h3 class="card-title mb-2 text-white">Welcome, {{ user.username }}</h3>
              <p class="text-light mb-4">Access and manage student clearance processes with ease.</p>
              <a href="{% url 'student_doc' %}" class="btn btn-light btn-lg px-4 shadow">
                <i class="fas fa-file-alt me-2"></i>Review Student Documents
              </a>
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}