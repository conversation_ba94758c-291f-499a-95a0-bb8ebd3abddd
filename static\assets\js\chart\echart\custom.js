(function ($) {
  require(["echarts"], function (echarts) {
    var chart = echarts.init(document.getElementById("area-echart"), null, {});

    var sampling = "none";

    chart.setOption({
      title: {
        text: "Rainfall Flow diagram",
        x: "center",
      },
      tooltip: {
        trigger: "axis",
        formatter: function (params) {
          return (
            params[0].name +
            "<br/>" +
            (params[0]
              ? params[0].seriesName + " : " + params[0].value + " (m^3/s)<br/>"
              : "") +
            (params[1]
              ? params[1].seriesName + " : " + -params[1].value + " (mm)"
              : "")
          );
        },
        axisPointer: {
          animation: false,
        },
      },
      legend: {
        data: ["Flow", "Rainfall"],
        x: "left",
      },
      toolbox: {
        show: true,
        feature: {
          mark: { show: true },
          dataView: { show: true, readOnly: false },
          magicType: { show: true, type: ["line", "bar"] },
          restore: { show: true },
          saveAsImage: { show: true },
        },
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
        },
        {
          type: "inside",
          show: true,
          realtime: true,
          start: 0,
          end: 100,
        },
      ],
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          axisLine: { onZero: false },
          data: [
            "2009/6/12 2:00",
            "2009/6/12 3:00",
            "2009/6/12 4:00",
            "2009/6/12 5:00",
            "2009/6/12 6:00",
            "2009/6/12 7:00",
            "2009/6/12 8:00",
            "2009/6/12 9:00",
            "2009/6/12 10:00",
            "2009/6/12 11:00",
            "2009/6/12 12:00",
            "2009/6/12 13:00",
            "2009/6/12 14:00",
            "2009/6/12 15:00",
            "2009/6/12 16:00",
            "2009/6/12 17:00",
            "2009/6/12 18:00",
            "2009/6/12 19:00",
            "2009/6/12 20:00",
            "2009/6/12 21:00",
            "2009/6/12 22:00",
            "2009/6/12 23:00",
            "2009/6/13 0:00",
            "2009/6/13 1:00",
            "2009/6/13 2:00",
            "2009/6/13 3:00",
            "2009/6/13 4:00",
            "2009/6/13 5:00",
            "2009/6/13 6:00",
            "2009/6/13 7:00",
            "2009/6/13 8:00",
            "2009/6/13 9:00",
            "2009/6/13 10:00",
            "2009/6/13 11:00",
            "2009/6/13 12:00",
            "2009/6/13 13:00",
            "2009/6/13 14:00",
            "2009/6/13 15:00",
            "2009/6/13 16:00",
            "2009/6/13 17:00",
            "2009/6/13 18:00",
            "2009/6/13 19:00",
            "2009/6/13 20:00",
            "2009/6/13 21:00",
            "2009/6/13 22:00",
            "2009/6/13 23:00",
            "2009/6/14 0:00",
            "2009/6/14 1:00",
            "2009/6/14 2:00",
            "2009/6/14 3:00",
            "2009/6/14 4:00",
            "2009/6/14 5:00",
            "2009/6/14 6:00",
            "2009/6/14 7:00",
            "2009/6/14 8:00",
            "2009/6/14 9:00",
            "2009/6/14 10:00",
            "2009/6/14 11:00",
            "2009/6/14 12:00",
            "2009/6/14 13:00",
            "2009/6/14 14:00",
            "2009/6/14 15:00",
            "2009/6/14 16:00",
            "2009/6/14 17:00",
            "2009/6/14 18:00",
            "2009/6/14 19:00",
            "2009/6/14 20:00",
            "2009/6/14 21:00",
            "2009/6/14 22:00",
            "2009/6/14 23:00",
            "2009/6/15 0:00",
            "2009/6/15 1:00",
            "2009/6/15 2:00",
            "2009/6/15 3:00",
            "2009/6/15 4:00",
            "2009/6/15 5:00",
            "2009/6/15 6:00",
            "2009/6/15 7:00",
            "2009/6/15 8:00",
            "2009/6/15 9:00",
            "2009/6/15 10:00",
            "2009/6/15 11:00",
            "2009/6/15 12:00",
            "2009/6/15 13:00",
            "2009/6/15 14:00",
            "2009/6/15 15:00",
            "2009/6/15 16:00",
            "2009/6/15 17:00",
            "2009/6/15 18:00",
            "2009/6/15 19:00",
            "2009/6/15 20:00",
            "2009/6/15 21:00",
            "2009/6/15 22:00",
            "2009/6/15 23:00",
            "2009/6/15 0:00",
            "2009/6/16 1:00",
            "2009/6/16 2:00",
            "2009/6/16 3:00",
            "2009/6/16 4:00",
            "2009/6/16 5:00",
            "2009/6/16 6:00",
            "2009/6/16 7:00",
            "2009/6/16 8:00",
            "2009/6/16 9:00",
            "2009/6/16 10:00",
            "2009/6/16 11:00",
            "2009/6/16 12:00",
            "2009/6/16 13:00",
            "2009/6/16 14:00",
            "2009/6/16 15:00",
            "2009/6/16 16:00",
            "2009/6/16 17:00",
            "2009/6/16 18:00",
            "2009/6/16 19:00",
            "2009/6/16 20:00",
            "2009/6/16 21:00",
            "2009/6/16 22:00",
            "2009/6/16 23:00",
            "2009/6/15 0:00",
            "2009/6/17 1:00",
            "2009/6/17 2:00",
            "2009/6/17 3:00",
            "2009/6/17 4:00",
            "2009/6/17 5:00",
            "2009/6/17 6:00",
            "2009/6/17 7:00",
            "2009/6/17 8:00",
            "2009/6/17 9:00",
            "2009/6/17 10:00",
            "2009/6/17 11:00",
            "2009/6/17 12:00",
            "2009/6/17 13:00",
            "2009/6/17 14:00",
            "2009/6/17 15:00",
            "2009/6/17 16:00",
            "2009/6/17 17:00",
            "2009/6/17 18:00",
            "2009/6/17 19:00",
            "2009/6/17 20:00",
            "2009/6/17 21:00",
            "2009/6/17 22:00",
            "2009/6/17 23:00",
            "2009/6/18 0:00",
            "2009/6/18 1:00",
            "2009/6/18 2:00",
            "2009/6/18 3:00",
            "2009/6/18 4:00",
            "2009/6/18 5:00",
            "2009/6/18 6:00",
            "2009/6/18 7:00",
            "2009/6/18 8:00",
            "2009/6/18 9:00",
            "2009/6/18 10:00",
            "2009/6/18 11:00",
            "2009/6/18 12:00",
            "2009/6/18 13:00",
            "2009/6/18 14:00",
            "2009/6/18 15:00",
            "2009/6/18 16:00",
            "2009/6/18 17:00",
            "2009/6/18 18:00",
            "2009/6/18 19:00",
            "2009/6/18 20:00",
            "2009/6/18 21:00",
            "2009/6/18 22:00",
            "2009/6/18 23:00",
            "2009/6/15 0:00",
            "2009/6/19 1:00",
            "2009/6/19 2:00",
            "2009/6/19 3:00",
            "2009/6/19 4:00",
            "2009/6/19 5:00",
            "2009/6/19 6:00",
            "2009/6/19 7:00",
            "2009/6/19 8:00",
            "2009/6/19 9:00",
            "2009/6/19 10:00",
            "2009/6/19 11:00",
            "2009/6/19 12:00",
            "2009/6/19 13:00",
            "2009/6/19 14:00",
            "2009/6/19 15:00",
            "2009/6/19 16:00",
            "2009/6/19 17:00",
            "2009/6/19 18:00",
            "2009/6/19 19:00",
            "2009/6/19 20:00",
            "2009/6/19 21:00",
            "2009/6/19 22:00",
            "2009/6/19 23:00",
            "2009/6/20 0:00",
            "2009/6/20 1:00",
            "2009/6/20 2:00",
            "2009/6/20 3:00",
            "2009/6/20 4:00",
            "2009/6/20 5:00",
            "2009/6/20 6:00",
            "2009/6/20 7:00",
            "2009/6/20 8:00",
            "2009/6/20 9:00",
            "2009/6/20 10:00",
            "2009/6/20 11:00",
            "2009/6/20 12:00",
            "2009/6/20 13:00",
            "2009/6/20 14:00",
            "2009/6/20 15:00",
            "2009/6/20 16:00",
            "2009/6/20 17:00",
            "2009/6/20 18:00",
            "2009/6/20 19:00",
            "2009/6/20 20:00",
            "2009/6/20 21:00",
            "2009/6/20 22:00",
            "2009/6/20 23:00",
            "2009/6/21 0:00",
            "2009/6/21 1:00",
            "2009/6/21 2:00",
            "2009/6/21 3:00",
            "2009/6/21 4:00",
            "2009/6/21 5:00",
            "2009/6/21 6:00",
            "2009/6/21 7:00",
            "2009/6/21 8:00",
            "2009/6/21 9:00",
            "2009/6/21 10:00",
            "2009/6/21 11:00",
            "2009/6/21 12:00",
            "2009/6/21 13:00",
            "2009/6/21 14:00",
            "2009/6/21 15:00",
            "2009/6/21 16:00",
            "2009/6/21 17:00",
            "2009/6/21 18:00",
            "2009/6/21 19:00",
            "2009/6/21 20:00",
            "2009/6/21 21:00",
            "2009/6/21 22:00",
            "2009/6/21 23:00",
            "2009/6/22 0:00",
            "2009/6/22 1:00",
            "2009/6/22 2:00",
            "2009/6/22 3:00",
            "2009/6/22 4:00",
            "2009/6/22 5:00",
            "2009/6/22 6:00",
            "2009/6/22 7:00",
            "2009/6/22 8:00",
            "2009/6/22 9:00",
            "2009/6/22 10:00",
            "2009/6/22 11:00",
            "2009/6/22 12:00",
            "2009/6/22 13:00",
            "2009/6/22 14:00",
            "2009/6/22 15:00",
            "2009/6/22 16:00",
            "2009/6/22 17:00",
            "2009/6/22 18:00",
            "2009/6/22 19:00",
            "2009/6/22 20:00",
            "2009/6/22 21:00",
            "2009/6/22 22:00",
            "2009/6/22 23:00",
            "2009/6/23 0:00",
            "2009/6/23 1:00",
            "2009/6/23 2:00",
            "2009/6/23 3:00",
            "2009/6/23 4:00",
            "2009/6/23 5:00",
            "2009/6/23 6:00",
            "2009/6/23 7:00",
            "2009/6/23 8:00",
            "2009/6/23 9:00",
            "2009/6/23 10:00",
            "2009/6/23 11:00",
            "2009/6/23 12:00",
            "2009/6/23 13:00",
            "2009/6/23 14:00",
            "2009/6/23 15:00",
            "2009/6/23 16:00",
            "2009/6/23 17:00",
            "2009/6/23 18:00",
            "2009/6/23 19:00",
            "2009/6/23 20:00",
            "2009/6/23 21:00",
            "2009/6/23 22:00",
            "2009/6/23 23:00",
            "2009/6/24 0:00",
            "2009/6/24 1:00",
            "2009/6/24 2:00",
            "2009/6/24 3:00",
            "2009/6/24 4:00",
            "2009/6/24 5:00",
            "2009/6/24 6:00",
            "2009/6/24 7:00",
            "2009/6/24 8:00",
            "2009/6/24 9:00",
            "2009/6/24 10:00",
            "2009/6/24 11:00",
            "2009/6/24 12:00",
            "2009/6/24 13:00",
            "2009/6/24 14:00",
            "2009/6/24 15:00",
            "2009/6/24 16:00",
            "2009/6/24 17:00",
            "2009/6/24 18:00",
            "2009/6/24 19:00",
            "2009/6/24 20:00",
            "2009/6/24 21:00",
            "2009/6/24 22:00",
            "2009/6/24 23:00",
            "2009/6/25 0:00",
            "2009/6/25 1:00",
            "2009/6/25 2:00",
            "2009/6/25 3:00",
            "2009/6/25 4:00",
            "2009/6/25 5:00",
            "2009/6/25 6:00",
            "2009/6/25 7:00",
            "2009/6/25 8:00",
            "2009/6/25 9:00",
            "2009/6/25 10:00",
            "2009/6/25 11:00",
            "2009/6/25 12:00",
            "2009/6/25 13:00",
            "2009/6/25 14:00",
            "2009/6/25 15:00",
            "2009/6/25 16:00",
            "2009/6/25 17:00",
            "2009/6/25 18:00",
            "2009/6/25 19:00",
            "2009/6/25 20:00",
            "2009/6/25 21:00",
            "2009/6/25 22:00",
            "2009/6/25 23:00",
            "2009/6/26 0:00",
            "2009/6/26 1:00",
            "2009/6/26 2:00",
            "2009/6/26 3:00",
            "2009/6/26 4:00",
            "2009/6/26 5:00",
            "2009/6/26 6:00",
            "2009/6/26 7:00",
            "2009/6/26 8:00",
            "2009/6/26 9:00",
            "2009/6/26 10:00",
            "2009/6/26 11:00",
            "2009/6/26 12:00",
            "2009/6/26 13:00",
            "2009/6/26 14:00",
            "2009/6/26 15:00",
            "2009/6/26 16:00",
            "2009/6/26 17:00",
            "2009/6/26 18:00",
            "2009/6/26 19:00",
            "2009/6/26 20:00",
            "2009/6/26 21:00",
            "2009/6/26 22:00",
            "2009/6/26 23:00",
            "2009/6/27 0:00",
            "2009/6/27 1:00",
            "2009/6/27 2:00",
            "2009/6/27 3:00",
            "2009/6/27 4:00",
            "2009/6/27 5:00",
            "2009/6/27 6:00",
            "2009/6/27 7:00",
            "2009/6/27 8:00",
            "2009/6/27 9:00",
            "2009/6/27 10:00",
            "2009/6/27 11:00",
            "2009/6/27 12:00",
            "2009/6/27 13:00",
            "2009/6/27 14:00",
            "2009/6/27 15:00",
            "2009/6/27 16:00",
            "2009/6/27 17:00",
            "2009/6/27 18:00",
            "2009/6/27 19:00",
            "2009/6/27 20:00",
            "2009/6/27 21:00",
            "2009/6/27 22:00",
            "2009/6/27 23:00",
            "2009/6/28 0:00",
            "2009/6/28 1:00",
            "2009/6/28 2:00",
            "2009/6/28 3:00",
            "2009/6/28 4:00",
            "2009/6/28 5:00",
            "2009/6/28 6:00",
            "2009/6/28 7:00",
            "2009/6/28 8:00",
            "2009/6/28 9:00",
            "2009/6/28 10:00",
            "2009/6/28 11:00",
            "2009/6/28 12:00",
            "2009/6/28 13:00",
            "2009/6/28 14:00",
            "2009/6/28 15:00",
            "2009/6/28 16:00",
            "2009/6/28 17:00",
            "2009/6/28 18:00",
            "2009/6/28 19:00",
            "2009/6/28 20:00",
            "2009/6/28 21:00",
            "2009/6/28 22:00",
            "2009/6/28 23:00",
            "2009/6/29 0:00",
            "2009/6/29 1:00",
            "2009/6/29 2:00",
            "2009/6/29 3:00",
            "2009/6/29 4:00",
            "2009/6/29 5:00",
            "2009/6/29 6:00",
            "2009/6/29 7:00",
            "2009/6/29 8:00",
            "2009/6/29 9:00",
            "2009/6/29 10:00",
            "2009/6/29 11:00",
            "2009/6/29 12:00",
            "2009/6/29 13:00",
            "2009/6/29 14:00",
            "2009/6/29 15:00",
            "2009/6/29 16:00",
            "2009/6/29 17:00",
            "2009/6/29 18:00",
            "2009/6/29 19:00",
            "2009/6/29 20:00",
            "2009/6/29 21:00",
            "2009/6/29 22:00",
            "2009/6/29 23:00",
            "2009/6/30 0:00",
            "2009/6/30 1:00",
            "2009/6/30 2:00",
            "2009/6/30 3:00",
            "2009/6/30 4:00",
            "2009/6/30 5:00",
            "2009/6/30 6:00",
            "2009/6/30 7:00",
            "2009/6/30 8:00",
            "2009/6/30 9:00",
            "2009/6/30 10:00",
            "2009/6/30 11:00",
            "2009/6/30 12:00",
            "2009/6/30 13:00",
            "2009/6/30 14:00",
            "2009/6/30 15:00",
            "2009/6/30 16:00",
            "2009/6/30 17:00",
            "2009/6/30 18:00",
            "2009/6/30 19:00",
            "2009/6/30 20:00",
            "2009/6/30 21:00",
            "2009/6/30 22:00",
            "2009/6/30 23:00",
            "2009/7/1 0:00",
            "2009/7/1 1:00",
            "2009/7/1 2:00",
            "2009/7/1 3:00",
            "2009/7/1 4:00",
            "2009/7/1 5:00",
            "2009/7/1 6:00",
            "2009/7/1 7:00",
            "2009/7/1 8:00",
            "2009/7/1 9:00",
            "2009/7/1 10:00",
            "2009/7/1 11:00",
            "2009/7/1 12:00",
            "2009/7/1 13:00",
            "2009/7/1 14:00",
            "2009/7/1 15:00",
            "2009/7/1 16:00",
            "2009/7/1 17:00",
            "2009/7/1 18:00",
            "2009/7/1 19:00",
            "2009/7/1 20:00",
            "2009/7/1 21:00",
            "2009/7/1 22:00",
            "2009/7/1 23:00",
            "2009/7/2 0:00",
            "2009/7/2 1:00",
            "2009/7/2 2:00",
            "2009/7/2 3:00",
            "2009/7/2 4:00",
            "2009/7/2 5:00",
            "2009/7/2 6:00",
            "2009/7/2 7:00",
            "2009/7/2 8:00",
            "2009/7/2 9:00",
            "2009/7/2 10:00",
            "2009/7/2 11:00",
            "2009/7/2 12:00",
            "2009/7/2 13:00",
            "2009/7/2 14:00",
            "2009/7/2 15:00",
            "2009/7/2 16:00",
            "2009/7/2 17:00",
            "2009/7/2 18:00",
            "2009/7/2 19:00",
            "2009/7/2 20:00",
            "2009/7/2 21:00",
            "2009/7/2 22:00",
            "2009/7/2 23:00",
            "2009/7/3 0:00",
            "2009/7/3 1:00",
            "2009/7/3 2:00",
            "2009/7/3 3:00",
            "2009/7/3 4:00",
            "2009/7/3 5:00",
            "2009/7/3 6:00",
            "2009/7/3 7:00",
            "2009/7/3 8:00",
            "2009/7/3 9:00",
            "2009/7/3 10:00",
            "2009/7/3 11:00",
            "2009/7/3 12:00",
            "2009/7/3 13:00",
            "2009/7/3 14:00",
            "2009/7/3 15:00",
            "2009/7/3 16:00",
            "2009/7/3 17:00",
            "2009/7/3 18:00",
            "2009/7/3 19:00",
            "2009/7/3 20:00",
            "2009/7/3 21:00",
            "2009/7/3 22:00",
            "2009/7/3 23:00",
            "2009/7/4 0:00",
            "2009/7/4 1:00",
            "2009/7/4 2:00",
            "2009/7/4 3:00",
            "2009/7/4 4:00",
            "2009/7/4 5:00",
            "2009/7/4 6:00",
            "2009/7/4 7:00",
            "2009/7/4 8:00",
            "2009/7/4 9:00",
            "2009/7/4 10:00",
            "2009/7/4 11:00",
            "2009/7/4 12:00",
            "2009/7/4 13:00",
            "2009/7/4 14:00",
            "2009/7/4 15:00",
            "2009/7/4 16:00",
            "2009/7/4 17:00",
            "2009/7/4 18:00",
            "2009/7/4 19:00",
            "2009/7/4 20:00",
            "2009/7/4 21:00",
            "2009/7/4 22:00",
            "2009/7/4 23:00",
            "2009/7/5 0:00",
            "2009/7/5 1:00",
            "2009/7/5 2:00",
            "2009/7/5 3:00",
            "2009/7/5 4:00",
            "2009/7/5 5:00",
            "2009/7/5 6:00",
            "2009/7/5 7:00",
            "2009/7/5 8:00",
            "2009/7/5 9:00",
            "2009/7/5 10:00",
            "2009/7/5 11:00",
            "2009/7/5 12:00",
            "2009/7/5 13:00",
            "2009/7/5 14:00",
            "2009/7/5 15:00",
            "2009/7/5 16:00",
            "2009/7/5 17:00",
            "2009/7/5 18:00",
            "2009/7/5 19:00",
            "2009/7/5 20:00",
            "2009/7/5 21:00",
            "2009/7/5 22:00",
            "2009/7/5 23:00",
            "2009/7/6 0:00",
            "2009/7/6 1:00",
            "2009/7/6 2:00",
            "2009/7/6 3:00",
            "2009/7/6 4:00",
            "2009/7/6 5:00",
            "2009/7/6 6:00",
            "2009/7/6 7:00",
            "2009/7/6 8:00",
            "2009/7/6 9:00",
            "2009/7/6 10:00",
            "2009/7/6 11:00",
            "2009/7/6 12:00",
            "2009/7/6 13:00",
            "2009/7/6 14:00",
            "2009/7/6 15:00",
            "2009/7/6 16:00",
            "2009/7/6 17:00",
            "2009/7/6 18:00",
            "2009/7/6 19:00",
            "2009/7/6 20:00",
            "2009/7/6 21:00",
            "2009/7/6 22:00",
            "2009/7/6 23:00",
            "2009/7/7 0:00",
            "2009/7/7 1:00",
            "2009/7/7 2:00",
            "2009/7/7 3:00",
            "2009/7/7 4:00",
            "2009/7/7 5:00",
            "2009/7/7 6:00",
            "2009/7/7 7:00",
            "2009/7/7 8:00",
            "2009/7/7 9:00",
            "2009/7/7 10:00",
            "2009/7/7 11:00",
            "2009/7/7 12:00",
            "2009/7/7 13:00",
            "2009/7/7 14:00",
            "2009/7/7 15:00",
            "2009/7/7 16:00",
            "2009/7/7 17:00",
            "2009/7/7 18:00",
            "2009/7/7 19:00",
            "2009/7/7 20:00",
            "2009/7/7 21:00",
            "2009/7/7 22:00",
            "2009/7/7 23:00",
            "2009/7/8 0:00",
            "2009/7/8 1:00",
            "2009/7/8 2:00",
            "2009/7/8 3:00",
            "2009/7/8 4:00",
            "2009/7/8 5:00",
            "2009/7/8 6:00",
            "2009/7/8 7:00",
            "2009/7/8 8:00",
            "2009/7/8 9:00",
            "2009/7/8 10:00",
            "2009/7/8 11:00",
            "2009/7/8 12:00",
            "2009/7/8 13:00",
            "2009/7/8 14:00",
            "2009/7/8 15:00",
            "2009/7/8 16:00",
            "2009/7/8 17:00",
            "2009/7/8 18:00",
            "2009/7/8 19:00",
            "2009/7/8 20:00",
            "2009/7/8 21:00",
            "2009/7/8 22:00",
            "2009/7/8 23:00",
            "2009/7/9 0:00",
            "2009/7/9 1:00",
            "2009/7/9 2:00",
            "2009/7/9 3:00",
            "2009/7/9 4:00",
            "2009/7/9 5:00",
            "2009/7/9 6:00",
            "2009/7/9 7:00",
            "2009/7/9 8:00",
            "2009/7/9 9:00",
            "2009/7/9 10:00",
            "2009/7/9 11:00",
            "2009/7/9 12:00",
            "2009/7/9 13:00",
            "2009/7/9 14:00",
            "2009/7/9 15:00",
            "2009/7/9 16:00",
            "2009/7/9 17:00",
            "2009/7/9 18:00",
            "2009/7/9 19:00",
            "2009/7/9 20:00",
            "2009/7/9 21:00",
            "2009/7/9 22:00",
            "2009/7/9 23:00",
            "2009/7/10 0:00",
            "2009/7/10 1:00",
            "2009/7/10 2:00",
            "2009/7/10 3:00",
            "2009/7/10 4:00",
            "2009/7/10 5:00",
            "2009/7/10 6:00",
            "2009/7/10 7:00",
            "2009/7/10 8:00",
            "2009/7/10 9:00",
            "2009/7/10 10:00",
            "2009/7/10 11:00",
            "2009/7/10 12:00",
            "2009/7/10 13:00",
            "2009/7/10 14:00",
            "2009/7/10 15:00",
            "2009/7/10 16:00",
            "2009/7/10 17:00",
            "2009/7/10 18:00",
            "2009/7/10 19:00",
            "2009/7/10 20:00",
            "2009/7/10 21:00",
            "2009/7/10 22:00",
            "2009/7/10 23:00",
            "2009/7/11 0:00",
            "2009/7/11 1:00",
            "2009/7/11 2:00",
            "2009/7/11 3:00",
            "2009/7/11 4:00",
            "2009/7/11 5:00",
            "2009/7/11 6:00",
            "2009/7/11 7:00",
            "2009/7/11 8:00",
            "2009/7/11 9:00",
            "2009/7/11 10:00",
            "2009/7/11 11:00",
            "2009/7/11 12:00",
            "2009/7/11 13:00",
            "2009/7/11 14:00",
            "2009/7/11 15:00",
            "2009/7/11 16:00",
            "2009/7/11 17:00",
            "2009/7/11 18:00",
            "2009/7/11 19:00",
            "2009/7/11 20:00",
            "2009/7/11 21:00",
            "2009/7/11 22:00",
            "2009/7/11 23:00",
            "2009/7/12 0:00",
            "2009/7/12 1:00",
            "2009/7/12 2:00",
            "2009/7/12 3:00",
            "2009/7/12 4:00",
            "2009/7/12 5:00",
            "2009/7/12 6:00",
            "2009/7/12 7:00",
            "2009/7/12 8:00",
            "2009/7/12 9:00",
            "2009/7/12 10:00",
            "2009/7/12 11:00",
            "2009/7/12 12:00",
            "2009/7/12 13:00",
            "2009/7/12 14:00",
            "2009/7/12 15:00",
            "2009/7/12 16:00",
            "2009/7/12 17:00",
            "2009/7/12 18:00",
            "2009/7/12 19:00",
            "2009/7/12 20:00",
            "2009/7/12 21:00",
            "2009/7/12 22:00",
            "2009/7/12 23:00",
            "2009/7/13 0:00",
            "2009/7/13 1:00",
            "2009/7/13 2:00",
            "2009/7/13 3:00",
            "2009/7/13 4:00",
            "2009/7/13 5:00",
            "2009/7/13 6:00",
            "2009/7/13 7:00",
            "2009/7/13 8:00",
            "2009/7/13 9:00",
            "2009/7/13 10:00",
            "2009/7/13 11:00",
            "2009/7/13 12:00",
            "2009/7/13 13:00",
            "2009/7/13 14:00",
            "2009/7/13 15:00",
            "2009/7/13 16:00",
            "2009/7/13 17:00",
            "2009/7/13 18:00",
            "2009/7/13 19:00",
            "2009/7/13 20:00",
            "2009/7/13 21:00",
            "2009/7/13 22:00",
            "2009/7/13 23:00",
            "2009/7/14 0:00",
            "2009/7/14 1:00",
            "2009/7/14 2:00",
            "2009/7/14 3:00",
            "2009/7/14 4:00",
            "2009/7/14 5:00",
            "2009/7/14 6:00",
            "2009/7/14 7:00",
            "2009/7/14 8:00",
            "2009/7/14 9:00",
            "2009/7/14 10:00",
            "2009/7/14 11:00",
            "2009/7/14 12:00",
            "2009/7/14 13:00",
            "2009/7/14 14:00",
            "2009/7/14 15:00",
            "2009/7/14 16:00",
            "2009/7/14 17:00",
            "2009/7/14 18:00",
            "2009/7/14 19:00",
            "2009/7/14 20:00",
            "2009/7/14 21:00",
            "2009/7/14 22:00",
            "2009/7/14 23:00",
            "2009/7/15 0:00",
            "2009/7/15 1:00",
            "2009/7/15 2:00",
            "2009/7/15 3:00",
            "2009/7/15 4:00",
            "2009/7/15 5:00",
            "2009/7/15 6:00",
            "2009/7/15 7:00",
            "2009/7/15 8:00",
            "2009/7/15 9:00",
            "2009/7/15 10:00",
            "2009/7/15 11:00",
            "2009/7/15 12:00",
            "2009/7/15 13:00",
            "2009/7/15 14:00",
            "2009/7/15 15:00",
            "2009/7/15 16:00",
            "2009/7/15 17:00",
            "2009/7/15 18:00",
            "2009/7/15 19:00",
            "2009/7/15 20:00",
            "2009/7/15 21:00",
            "2009/7/15 22:00",
            "2009/7/15 23:00",
            "2009/7/16 0:00",
            "2009/7/16 1:00",
            "2009/7/16 2:00",
            "2009/7/16 3:00",
            "2009/7/16 4:00",
            "2009/7/16 5:00",
            "2009/7/16 6:00",
            "2009/7/16 7:00",
            "2009/7/16 8:00",
            "2009/7/16 9:00",
            "2009/7/16 10:00",
            "2009/7/16 11:00",
            "2009/7/16 12:00",
            "2009/7/16 13:00",
            "2009/7/16 14:00",
            "2009/7/16 15:00",
            "2009/7/16 16:00",
            "2009/7/16 17:00",
            "2009/7/16 18:00",
            "2009/7/16 19:00",
            "2009/7/16 20:00",
            "2009/7/16 21:00",
            "2009/7/16 22:00",
            "2009/7/16 23:00",
            "2009/7/17 0:00",
            "2009/7/17 1:00",
            "2009/7/17 2:00",
            "2009/7/17 3:00",
            "2009/7/17 4:00",
            "2009/7/17 5:00",
            "2009/7/17 6:00",
            "2009/7/17 7:00",
            "2009/7/17 8:00",
            "2009/7/17 9:00",
            "2009/7/17 10:00",
            "2009/7/17 11:00",
            "2009/7/17 12:00",
            "2009/7/17 13:00",
            "2009/7/17 14:00",
            "2009/7/17 15:00",
            "2009/7/17 16:00",
            "2009/7/17 17:00",
            "2009/7/17 18:00",
            "2009/7/17 19:00",
            "2009/7/17 20:00",
            "2009/7/17 21:00",
            "2009/7/17 22:00",
            "2009/7/17 23:00",
            "2009/7/18 0:00",
            "2009/7/18 1:00",
            "2009/7/18 2:00",
            "2009/7/18 3:00",
            "2009/7/18 4:00",
            "2009/7/18 5:00",
            "2009/7/18 6:00",
            "2009/7/18 7:00",
            "2009/7/18 8:00",
            "2009/7/18 9:00",
            "2009/7/18 10:00",
            "2009/7/18 11:00",
            "2009/7/18 12:00",
            "2009/7/18 13:00",
            "2009/7/18 14:00",
            "2009/7/18 15:00",
            "2009/7/18 16:00",
            "2009/7/18 17:00",
            "2009/7/18 18:00",
            "2009/7/18 19:00",
            "2009/7/18 20:00",
            "2009/7/18 21:00",
            "2009/7/18 22:00",
            "2009/7/18 23:00",
            "2009/7/19 0:00",
            "2009/7/19 1:00",
            "2009/7/19 2:00",
            "2009/7/19 3:00",
            "2009/7/19 4:00",
            "2009/7/19 5:00",
            "2009/7/19 6:00",
            "2009/7/19 7:00",
            "2009/7/19 8:00",
            "2009/7/19 9:00",
            "2009/7/19 10:00",
            "2009/7/19 11:00",
            "2009/7/19 12:00",
            "2009/7/19 13:00",
            "2009/7/19 14:00",
            "2009/7/19 15:00",
            "2009/7/19 16:00",
            "2009/7/19 17:00",
            "2009/7/19 18:00",
            "2009/7/19 19:00",
            "2009/7/19 20:00",
            "2009/7/19 21:00",
            "2009/7/19 22:00",
            "2009/7/19 23:00",
            "2009/7/20 0:00",
            "2009/7/20 1:00",
            "2009/7/20 2:00",
            "2009/7/20 3:00",
            "2009/7/20 4:00",
            "2009/7/20 5:00",
            "2009/7/20 6:00",
            "2009/7/20 7:00",
            "2009/7/20 8:00",
            "2009/7/20 9:00",
            "2009/7/20 10:00",
            "2009/7/20 11:00",
            "2009/7/20 12:00",
            "2009/7/20 13:00",
            "2009/7/20 14:00",
            "2009/7/20 15:00",
            "2009/7/20 16:00",
            "2009/7/20 17:00",
            "2009/7/20 18:00",
            "2009/7/20 19:00",
            "2009/7/20 20:00",
            "2009/7/20 21:00",
            "2009/7/20 22:00",
            "2009/7/20 23:00",
            "2009/7/21 0:00",
            "2009/7/21 1:00",
            "2009/7/21 2:00",
            "2009/7/21 3:00",
            "2009/7/21 4:00",
            "2009/7/21 5:00",
            "2009/7/21 6:00",
            "2009/7/21 7:00",
            "2009/7/21 8:00",
            "2009/7/21 9:00",
            "2009/7/21 10:00",
            "2009/7/21 11:00",
            "2009/7/21 12:00",
            "2009/7/21 13:00",
            "2009/7/21 14:00",
            "2009/7/21 15:00",
            "2009/7/21 16:00",
            "2009/7/21 17:00",
            "2009/7/21 18:00",
            "2009/7/21 19:00",
            "2009/7/21 20:00",
            "2009/7/21 21:00",
            "2009/7/21 22:00",
            "2009/7/21 23:00",
            "2009/7/22 0:00",
            "2009/7/22 1:00",
            "2009/7/22 2:00",
            "2009/7/22 3:00",
            "2009/7/22 4:00",
            "2009/7/22 5:00",
            "2009/7/22 6:00",
            "2009/7/22 7:00",
            "2009/7/22 8:00",
            "2009/7/22 9:00",
            "2009/7/22 10:00",
            "2009/7/22 11:00",
            "2009/7/22 12:00",
            "2009/7/22 13:00",
            "2009/7/22 14:00",
            "2009/7/22 15:00",
            "2009/7/22 16:00",
            "2009/7/22 17:00",
            "2009/7/22 18:00",
            "2009/7/22 19:00",
            "2009/7/22 20:00",
            "2009/7/22 21:00",
            "2009/7/22 22:00",
            "2009/7/22 23:00",
            "2009/7/23 0:00",
            "2009/7/23 1:00",
            "2009/7/23 2:00",
            "2009/7/23 3:00",
            "2009/7/23 4:00",
            "2009/7/23 5:00",
            "2009/7/23 6:00",
            "2009/7/23 7:00",
            "2009/7/23 8:00",
            "2009/7/23 9:00",
            "2009/7/23 10:00",
            "2009/7/23 11:00",
            "2009/7/23 12:00",
            "2009/7/23 13:00",
            "2009/7/23 14:00",
            "2009/7/23 15:00",
            "2009/7/23 16:00",
            "2009/7/23 17:00",
            "2009/7/23 18:00",
            "2009/7/23 19:00",
            "2009/7/23 20:00",
            "2009/7/23 21:00",
            "2009/7/23 22:00",
            "2009/7/23 23:00",
            "2009/7/24 0:00",
            "2009/7/24 1:00",
            "2009/7/24 2:00",
            "2009/7/24 3:00",
            "2009/7/24 4:00",
            "2009/7/24 5:00",
            "2009/7/24 6:00",
            "2009/7/24 7:00",
            "2009/7/24 8:00",
            "2009/7/24 9:00",
            "2009/7/24 10:00",
            "2009/7/24 11:00",
            "2009/7/24 12:00",
            "2009/7/24 13:00",
            "2009/7/24 14:00",
            "2009/7/24 15:00",
            "2009/7/24 16:00",
            "2009/7/24 17:00",
            "2009/7/24 18:00",
            "2009/7/24 19:00",
            "2009/7/24 20:00",
            "2009/7/24 21:00",
            "2009/7/24 22:00",
            "2009/7/24 23:00",
            "2009/7/25 0:00",
            "2009/7/25 1:00",
            "2009/7/25 2:00",
            "2009/7/25 3:00",
            "2009/7/25 4:00",
            "2009/7/25 5:00",
            "2009/7/25 6:00",
            "2009/7/25 7:00",
            "2009/7/25 8:00",
            "2009/7/25 9:00",
            "2009/7/25 10:00",
            "2009/7/25 11:00",
            "2009/7/25 12:00",
            "2009/7/25 13:00",
            "2009/7/25 14:00",
            "2009/7/25 15:00",
            "2009/7/25 16:00",
            "2009/7/25 17:00",
            "2009/7/25 18:00",
            "2009/7/25 19:00",
            "2009/7/25 20:00",
            "2009/7/25 21:00",
            "2009/7/25 22:00",
            "2009/7/25 23:00",
            "2009/7/26 0:00",
            "2009/7/26 1:00",
            "2009/7/26 2:00",
            "2009/7/26 3:00",
            "2009/7/26 4:00",
            "2009/7/26 5:00",
            "2009/7/26 6:00",
            "2009/7/26 7:00",
            "2009/7/26 8:00",
            "2009/7/26 9:00",
            "2009/7/26 10:00",
            "2009/7/26 11:00",
            "2009/7/26 12:00",
            "2009/7/26 13:00",
            "2009/7/26 14:00",
            "2009/7/26 15:00",
            "2009/7/26 16:00",
            "2009/7/26 17:00",
            "2009/7/26 18:00",
            "2009/7/26 19:00",
            "2009/7/26 20:00",
            "2009/7/26 21:00",
            "2009/7/26 22:00",
            "2009/7/26 23:00",
            "2009/7/27 0:00",
            "2009/7/27 1:00",
            "2009/7/27 2:00",
            "2009/7/27 3:00",
            "2009/7/27 4:00",
            "2009/7/27 5:00",
            "2009/7/27 6:00",
            "2009/7/27 7:00",
            "2009/7/27 8:00",
            "2009/7/27 9:00",
            "2009/7/27 10:00",
            "2009/7/27 11:00",
            "2009/7/27 12:00",
            "2009/7/27 13:00",
            "2009/7/27 14:00",
            "2009/7/27 15:00",
            "2009/7/27 16:00",
            "2009/7/27 17:00",
            "2009/7/27 18:00",
            "2009/7/27 19:00",
            "2009/7/27 20:00",
            "2009/7/27 21:00",
            "2009/7/27 22:00",
            "2009/7/27 23:00",
            "2009/7/28 0:00",
            "2009/7/28 1:00",
            "2009/7/28 2:00",
            "2009/7/28 3:00",
            "2009/7/28 4:00",
            "2009/7/28 5:00",
            "2009/7/28 6:00",
            "2009/7/28 7:00",
            "2009/7/28 8:00",
            "2009/7/28 9:00",
            "2009/7/28 10:00",
            "2009/7/28 11:00",
            "2009/7/28 12:00",
            "2009/7/28 13:00",
            "2009/7/28 14:00",
            "2009/7/28 15:00",
            "2009/7/28 16:00",
            "2009/7/28 17:00",
            "2009/7/28 18:00",
            "2009/7/28 19:00",
            "2009/7/28 20:00",
            "2009/7/28 21:00",
            "2009/7/28 22:00",
            "2009/7/28 23:00",
            "2009/7/29 0:00",
            "2009/7/29 1:00",
            "2009/7/29 2:00",
            "2009/7/29 3:00",
            "2009/7/29 4:00",
            "2009/7/29 5:00",
            "2009/7/29 6:00",
            "2009/7/29 7:00",
            "2009/7/29 8:00",
            "2009/7/29 9:00",
            "2009/7/29 10:00",
            "2009/7/29 11:00",
            "2009/7/29 12:00",
            "2009/7/29 13:00",
            "2009/7/29 14:00",
            "2009/7/29 15:00",
            "2009/7/29 16:00",
            "2009/7/29 17:00",
            "2009/7/29 18:00",
            "2009/7/29 19:00",
            "2009/7/29 20:00",
            "2009/7/29 21:00",
            "2009/7/29 22:00",
            "2009/7/29 23:00",
            "2009/7/30 0:00",
            "2009/7/30 1:00",
            "2009/7/30 2:00",
            "2009/7/30 3:00",
            "2009/7/30 4:00",
            "2009/7/30 5:00",
            "2009/7/30 6:00",
            "2009/7/30 7:00",
            "2009/7/30 8:00",
            "2009/7/30 9:00",
            "2009/7/30 10:00",
            "2009/7/30 11:00",
            "2009/7/30 12:00",
            "2009/7/30 13:00",
            "2009/7/30 14:00",
            "2009/7/30 15:00",
            "2009/7/30 16:00",
            "2009/7/30 17:00",
            "2009/7/30 18:00",
            "2009/7/30 19:00",
            "2009/7/30 20:00",
            "2009/7/30 21:00",
            "2009/7/30 22:00",
            "2009/7/30 23:00",
            "2009/7/31 0:00",
            "2009/7/31 1:00",
            "2009/7/31 2:00",
            "2009/7/31 3:00",
            "2009/7/31 4:00",
            "2009/7/31 5:00",
            "2009/7/31 6:00",
            "2009/7/31 7:00",
            "2009/7/31 8:00",
            "2009/7/31 9:00",
            "2009/7/31 10:00",
            "2009/7/31 11:00",
            "2009/7/31 12:00",
            "2009/7/31 13:00",
            "2009/7/31 14:00",
            "2009/7/31 15:00",
            "2009/7/31 16:00",
            "2009/7/31 17:00",
            "2009/7/31 18:00",
            "2009/7/31 19:00",
            "2009/7/31 20:00",
            "2009/7/31 21:00",
            "2009/7/31 22:00",
            "2009/7/31 23:00",
            "2009/8/1 0:00",
            "2009/8/1 1:00",
            "2009/8/1 2:00",
            "2009/8/1 3:00",
            "2009/8/1 4:00",
            "2009/8/1 5:00",
            "2009/8/1 6:00",
            "2009/8/1 7:00",
            "2009/8/1 8:00",
            "2009/8/1 9:00",
            "2009/8/1 10:00",
            "2009/8/1 11:00",
            "2009/8/1 12:00",
            "2009/8/1 13:00",
            "2009/8/1 14:00",
            "2009/8/1 15:00",
            "2009/8/1 16:00",
            "2009/8/1 17:00",
            "2009/8/1 18:00",
            "2009/8/1 19:00",
            "2009/8/1 20:00",
            "2009/8/1 21:00",
            "2009/8/1 22:00",
            "2009/8/1 23:00",
            "2009/8/2 0:00",
            "2009/8/2 1:00",
            "2009/8/2 2:00",
            "2009/8/2 3:00",
            "2009/8/2 4:00",
            "2009/8/2 5:00",
            "2009/8/2 6:00",
            "2009/8/2 7:00",
            "2009/8/2 8:00",
            "2009/8/2 9:00",
            "2009/8/2 10:00",
            "2009/8/2 11:00",
            "2009/8/2 12:00",
            "2009/8/2 13:00",
            "2009/8/2 14:00",
            "2009/8/2 15:00",
            "2009/8/2 16:00",
            "2009/8/2 17:00",
            "2009/8/2 18:00",
            "2009/8/2 19:00",
            "2009/8/2 20:00",
            "2009/8/2 21:00",
            "2009/8/2 22:00",
            "2009/8/2 23:00",
            "2009/8/3 0:00",
            "2009/8/3 1:00",
            "2009/8/3 2:00",
            "2009/8/3 3:00",
            "2009/8/3 4:00",
            "2009/8/3 5:00",
            "2009/8/3 6:00",
            "2009/8/3 7:00",
            "2009/8/3 8:00",
            "2009/8/3 9:00",
            "2009/8/3 10:00",
            "2009/8/3 11:00",
            "2009/8/3 12:00",
            "2009/8/3 13:00",
            "2009/8/3 14:00",
            "2009/8/3 15:00",
            "2009/8/3 16:00",
            "2009/8/3 17:00",
            "2009/8/3 18:00",
            "2009/8/3 19:00",
            "2009/8/3 20:00",
            "2009/8/3 21:00",
            "2009/8/3 22:00",
            "2009/8/3 23:00",
            "2009/8/4 0:00",
            "2009/8/4 1:00",
            "2009/8/4 2:00",
            "2009/8/4 3:00",
            "2009/8/4 4:00",
            "2009/8/4 5:00",
            "2009/8/4 6:00",
            "2009/8/4 7:00",
            "2009/8/4 8:00",
            "2009/8/4 9:00",
            "2009/8/4 10:00",
            "2009/8/4 11:00",
            "2009/8/4 12:00",
            "2009/8/4 13:00",
            "2009/8/4 14:00",
            "2009/8/4 15:00",
            "2009/8/4 16:00",
            "2009/8/4 17:00",
            "2009/8/4 18:00",
            "2009/8/4 19:00",
            "2009/8/4 20:00",
            "2009/8/4 21:00",
            "2009/8/4 22:00",
            "2009/8/4 23:00",
            "2009/8/5 0:00",
            "2009/8/5 1:00",
            "2009/8/5 2:00",
            "2009/8/5 3:00",
            "2009/8/5 4:00",
            "2009/8/5 5:00",
            "2009/8/5 6:00",
            "2009/8/5 7:00",
            "2009/8/5 8:00",
            "2009/8/5 9:00",
            "2009/8/5 10:00",
            "2009/8/5 11:00",
            "2009/8/5 12:00",
            "2009/8/5 13:00",
            "2009/8/5 14:00",
            "2009/8/5 15:00",
            "2009/8/5 16:00",
            "2009/8/5 17:00",
            "2009/8/5 18:00",
            "2009/8/5 19:00",
            "2009/8/5 20:00",
            "2009/8/5 21:00",
            "2009/8/5 22:00",
            "2009/8/5 23:00",
            "2009/8/6 0:00",
            "2009/8/6 1:00",
            "2009/8/6 2:00",
            "2009/8/6 3:00",
            "2009/8/6 4:00",
            "2009/8/6 5:00",
            "2009/8/6 6:00",
            "2009/8/6 7:00",
            "2009/8/6 8:00",
            "2009/8/6 9:00",
            "2009/8/6 10:00",
            "2009/8/6 11:00",
            "2009/8/6 12:00",
            "2009/8/6 13:00",
            "2009/8/6 14:00",
            "2009/8/6 15:00",
            "2009/8/6 16:00",
            "2009/8/6 17:00",
            "2009/8/6 18:00",
            "2009/8/6 19:00",
            "2009/8/6 20:00",
            "2009/8/6 21:00",
            "2009/8/6 22:00",
            "2009/8/6 23:00",
            "2009/8/7 0:00",
            "2009/8/7 1:00",
            "2009/8/7 2:00",
            "2009/8/7 3:00",
            "2009/8/7 4:00",
            "2009/8/7 5:00",
            "2009/8/7 6:00",
            "2009/8/7 7:00",
            "2009/8/7 8:00",
            "2009/8/7 9:00",
            "2009/8/7 10:00",
            "2009/8/7 11:00",
            "2009/8/7 12:00",
            "2009/8/7 13:00",
            "2009/8/7 14:00",
            "2009/8/7 15:00",
            "2009/8/7 16:00",
            "2009/8/7 17:00",
            "2009/8/7 18:00",
            "2009/8/7 19:00",
            "2009/8/7 20:00",
            "2009/8/7 21:00",
            "2009/8/7 22:00",
            "2009/8/7 23:00",
            "2009/8/8 0:00",
            "2009/8/8 1:00",
            "2009/8/8 2:00",
            "2009/8/8 3:00",
            "2009/8/8 4:00",
            "2009/8/8 5:00",
            "2009/8/8 6:00",
            "2009/8/8 7:00",
            "2009/8/8 8:00",
            "2009/8/8 9:00",
            "2009/8/8 10:00",
            "2009/8/8 11:00",
            "2009/8/8 12:00",
            "2009/8/8 13:00",
            "2009/8/8 14:00",
            "2009/8/8 15:00",
            "2009/8/8 16:00",
            "2009/8/8 17:00",
            "2009/8/8 18:00",
            "2009/8/8 19:00",
            "2009/8/8 20:00",
            "2009/8/8 21:00",
            "2009/8/8 22:00",
            "2009/8/8 23:00",
            "2009/8/9 0:00",
            "2009/8/9 1:00",
            "2009/8/9 2:00",
            "2009/8/9 3:00",
            "2009/8/9 4:00",
            "2009/8/9 5:00",
            "2009/8/9 6:00",
            "2009/8/9 7:00",
            "2009/8/9 8:00",
            "2009/8/9 9:00",
            "2009/8/9 10:00",
            "2009/8/9 11:00",
            "2009/8/9 12:00",
            "2009/8/9 13:00",
            "2009/8/9 14:00",
            "2009/8/9 15:00",
            "2009/8/9 16:00",
            "2009/8/9 17:00",
            "2009/8/9 18:00",
            "2009/8/9 19:00",
            "2009/8/9 20:00",
            "2009/8/9 21:00",
            "2009/8/9 22:00",
            "2009/8/9 23:00",
            "2009/8/10 0:00",
            "2009/8/10 1:00",
            "2009/8/10 2:00",
            "2009/8/10 3:00",
            "2009/8/10 4:00",
            "2009/8/10 5:00",
            "2009/8/10 6:00",
            "2009/8/10 7:00",
            "2009/8/10 8:00",
            "2009/8/10 9:00",
            "2009/8/10 10:00",
            "2009/8/10 11:00",
            "2009/8/10 12:00",
            "2009/8/10 13:00",
            "2009/8/10 14:00",
            "2009/8/10 15:00",
            "2009/8/10 16:00",
            "2009/8/10 17:00",
            "2009/8/10 18:00",
            "2009/8/10 19:00",
            "2009/8/10 20:00",
            "2009/8/10 21:00",
            "2009/8/10 22:00",
            "2009/8/10 23:00",
            "2009/8/11 0:00",
            "2009/8/11 1:00",
            "2009/8/11 2:00",
            "2009/8/11 3:00",
            "2009/8/11 4:00",
            "2009/8/11 5:00",
            "2009/8/11 6:00",
            "2009/8/11 7:00",
            "2009/8/11 8:00",
            "2009/8/11 9:00",
            "2009/8/11 10:00",
            "2009/8/11 11:00",
            "2009/8/11 12:00",
            "2009/8/11 13:00",
            "2009/8/11 14:00",
            "2009/8/11 15:00",
            "2009/8/11 16:00",
            "2009/8/11 17:00",
            "2009/8/11 18:00",
            "2009/8/11 19:00",
            "2009/8/11 20:00",
            "2009/8/11 21:00",
            "2009/8/11 22:00",
            "2009/8/11 23:00",
            "2009/8/12 0:00",
            "2009/8/12 1:00",
            "2009/8/12 2:00",
            "2009/8/12 3:00",
            "2009/8/12 4:00",
            "2009/8/12 5:00",
            "2009/8/12 6:00",
            "2009/8/12 7:00",
            "2009/8/12 8:00",
            "2009/8/12 9:00",
            "2009/8/12 10:00",
            "2009/8/12 11:00",
            "2009/8/12 12:00",
            "2009/8/12 13:00",
            "2009/8/12 14:00",
            "2009/8/12 15:00",
            "2009/8/12 16:00",
            "2009/8/12 17:00",
            "2009/8/12 18:00",
            "2009/8/12 19:00",
            "2009/8/12 20:00",
            "2009/8/12 21:00",
            "2009/8/12 22:00",
            "2009/8/12 23:00",
            "2009/8/13 0:00",
            "2009/8/13 1:00",
            "2009/8/13 2:00",
            "2009/8/13 3:00",
            "2009/8/13 4:00",
            "2009/8/13 5:00",
            "2009/8/13 6:00",
            "2009/8/13 7:00",
            "2009/8/13 8:00",
            "2009/8/13 9:00",
            "2009/8/13 10:00",
            "2009/8/13 11:00",
            "2009/8/13 12:00",
            "2009/8/13 13:00",
            "2009/8/13 14:00",
            "2009/8/13 15:00",
            "2009/8/13 16:00",
            "2009/8/13 17:00",
            "2009/8/13 18:00",
            "2009/8/13 19:00",
            "2009/8/13 20:00",
            "2009/8/13 21:00",
            "2009/8/13 22:00",
            "2009/8/13 23:00",
            "2009/8/14 0:00",
            "2009/8/14 1:00",
            "2009/8/14 2:00",
            "2009/8/14 3:00",
            "2009/8/14 4:00",
            "2009/8/14 5:00",
            "2009/8/14 6:00",
            "2009/8/14 7:00",
            "2009/8/14 8:00",
            "2009/8/14 9:00",
            "2009/8/14 10:00",
            "2009/8/14 11:00",
            "2009/8/14 12:00",
            "2009/8/14 13:00",
            "2009/8/14 14:00",
            "2009/8/14 15:00",
            "2009/8/14 16:00",
            "2009/8/14 17:00",
            "2009/8/14 18:00",
            "2009/8/14 19:00",
            "2009/8/14 20:00",
            "2009/8/14 21:00",
            "2009/8/14 22:00",
            "2009/8/14 23:00",
            "2009/8/15 0:00",
            "2009/8/15 1:00",
            "2009/8/15 2:00",
            "2009/8/15 3:00",
            "2009/8/15 4:00",
            "2009/8/15 5:00",
            "2009/8/15 6:00",
            "2009/8/15 7:00",
            "2009/8/15 8:00",
            "2009/8/15 9:00",
            "2009/8/15 10:00",
            "2009/8/15 11:00",
            "2009/8/15 12:00",
            "2009/8/15 13:00",
            "2009/8/15 14:00",
            "2009/8/15 15:00",
            "2009/8/15 16:00",
            "2009/8/15 17:00",
            "2009/8/15 18:00",
            "2009/8/15 19:00",
            "2009/8/15 20:00",
            "2009/8/15 21:00",
            "2009/8/15 22:00",
            "2009/8/15 23:00",
            "2009/8/16 0:00",
            "2009/8/16 1:00",
            "2009/8/16 2:00",
            "2009/8/16 3:00",
            "2009/8/16 4:00",
            "2009/8/16 5:00",
            "2009/8/16 6:00",
            "2009/8/16 7:00",
            "2009/8/16 8:00",
            "2009/8/16 9:00",
            "2009/8/16 10:00",
            "2009/8/16 11:00",
            "2009/8/16 12:00",
            "2009/8/16 13:00",
            "2009/8/16 14:00",
            "2009/8/16 15:00",
            "2009/8/16 16:00",
            "2009/8/16 17:00",
            "2009/8/16 18:00",
            "2009/8/16 19:00",
            "2009/8/16 20:00",
            "2009/8/16 21:00",
            "2009/8/16 22:00",
            "2009/8/16 23:00",
            "2009/8/17 0:00",
            "2009/8/17 1:00",
            "2009/8/17 2:00",
            "2009/8/17 3:00",
            "2009/8/17 4:00",
            "2009/8/17 5:00",
            "2009/8/17 6:00",
            "2009/8/17 7:00",
            "2009/8/17 8:00",
            "2009/8/17 9:00",
            "2009/8/17 10:00",
            "2009/8/17 11:00",
            "2009/8/17 12:00",
            "2009/8/17 13:00",
            "2009/8/17 14:00",
            "2009/8/17 15:00",
            "2009/8/17 16:00",
            "2009/8/17 17:00",
            "2009/8/17 18:00",
            "2009/8/17 19:00",
            "2009/8/17 20:00",
            "2009/8/17 21:00",
            "2009/8/17 22:00",
            "2009/8/17 23:00",
            "2009/8/18 0:00",
            "2009/8/18 1:00",
            "2009/8/18 2:00",
            "2009/8/18 3:00",
            "2009/8/18 4:00",
            "2009/8/18 5:00",
            "2009/8/18 6:00",
            "2009/8/18 7:00",
            "2009/8/18 8:00",
            "2009/8/18 9:00",
            "2009/8/18 10:00",
            "2009/8/18 11:00",
            "2009/8/18 12:00",
            "2009/8/18 13:00",
            "2009/8/18 14:00",
            "2009/8/18 15:00",
            "2009/8/18 16:00",
            "2009/8/18 17:00",
            "2009/8/18 18:00",
            "2009/8/18 19:00",
            "2009/8/18 20:00",
            "2009/8/18 21:00",
            "2009/8/18 22:00",
            "2009/8/18 23:00",
            "2009/8/19 0:00",
            "2009/8/19 1:00",
            "2009/8/19 2:00",
            "2009/8/19 3:00",
            "2009/8/19 4:00",
            "2009/8/19 5:00",
            "2009/8/19 6:00",
            "2009/8/19 7:00",
            "2009/8/19 8:00",
            "2009/8/19 9:00",
            "2009/8/19 10:00",
            "2009/8/19 11:00",
            "2009/8/19 12:00",
            "2009/8/19 13:00",
            "2009/8/19 14:00",
            "2009/8/19 15:00",
            "2009/8/19 16:00",
            "2009/8/19 17:00",
            "2009/8/19 18:00",
            "2009/8/19 19:00",
            "2009/8/19 20:00",
            "2009/8/19 21:00",
            "2009/8/19 22:00",
            "2009/8/19 23:00",
            "2009/8/20 0:00",
            "2009/8/20 1:00",
            "2009/8/20 2:00",
            "2009/8/20 3:00",
            "2009/8/20 4:00",
            "2009/8/20 5:00",
            "2009/8/20 6:00",
            "2009/8/20 7:00",
            "2009/8/20 8:00",
            "2009/8/20 9:00",
            "2009/8/20 10:00",
            "2009/8/20 11:00",
            "2009/8/20 12:00",
            "2009/8/20 13:00",
            "2009/8/20 14:00",
            "2009/8/20 15:00",
            "2009/8/20 16:00",
            "2009/8/20 17:00",
            "2009/8/20 18:00",
            "2009/8/20 19:00",
            "2009/8/20 20:00",
            "2009/8/20 21:00",
            "2009/8/20 22:00",
            "2009/8/20 23:00",
            "2009/8/21 0:00",
            "2009/8/21 1:00",
            "2009/8/21 2:00",
            "2009/8/21 3:00",
            "2009/8/21 4:00",
            "2009/8/21 5:00",
            "2009/8/21 6:00",
            "2009/8/21 7:00",
            "2009/8/21 8:00",
            "2009/8/21 9:00",
            "2009/8/21 10:00",
            "2009/8/21 11:00",
            "2009/8/21 12:00",
            "2009/8/21 13:00",
            "2009/8/21 14:00",
            "2009/8/21 15:00",
            "2009/8/21 16:00",
            "2009/8/21 17:00",
            "2009/8/21 18:00",
            "2009/8/21 19:00",
            "2009/8/21 20:00",
            "2009/8/21 21:00",
            "2009/8/21 22:00",
            "2009/8/21 23:00",
            "2009/8/22 0:00",
            "2009/8/22 1:00",
            "2009/8/22 2:00",
            "2009/8/22 3:00",
            "2009/8/22 4:00",
            "2009/8/22 5:00",
            "2009/8/22 6:00",
            "2009/8/22 7:00",
            "2009/8/22 8:00",
            "2009/8/22 9:00",
            "2009/8/22 10:00",
            "2009/8/22 11:00",
            "2009/8/22 12:00",
            "2009/8/22 13:00",
            "2009/8/22 14:00",
            "2009/8/22 15:00",
            "2009/8/22 16:00",
            "2009/8/22 17:00",
            "2009/8/22 18:00",
            "2009/8/22 19:00",
            "2009/8/22 20:00",
            "2009/8/22 21:00",
            "2009/8/22 22:00",
            "2009/8/22 23:00",
            "2009/8/23 0:00",
            "2009/8/23 1:00",
            "2009/8/23 2:00",
            "2009/8/23 3:00",
            "2009/8/23 4:00",
            "2009/8/23 5:00",
            "2009/8/23 6:00",
            "2009/8/23 7:00",
            "2009/8/23 8:00",
            "2009/8/23 9:00",
            "2009/8/23 10:00",
            "2009/8/23 11:00",
            "2009/8/23 12:00",
            "2009/8/23 13:00",
            "2009/8/23 14:00",
            "2009/8/23 15:00",
            "2009/8/23 16:00",
            "2009/8/23 17:00",
            "2009/8/23 18:00",
            "2009/8/23 19:00",
            "2009/8/23 20:00",
            "2009/8/23 21:00",
            "2009/8/23 22:00",
            "2009/8/23 23:00",
            "2009/8/24 0:00",
            "2009/8/24 1:00",
            "2009/8/24 2:00",
            "2009/8/24 3:00",
            "2009/8/24 4:00",
            "2009/8/24 5:00",
            "2009/8/24 6:00",
            "2009/8/24 7:00",
            "2009/8/24 8:00",
            "2009/8/24 9:00",
            "2009/8/24 10:00",
            "2009/8/24 11:00",
            "2009/8/24 12:00",
            "2009/8/24 13:00",
            "2009/8/24 14:00",
            "2009/8/24 15:00",
            "2009/8/24 16:00",
            "2009/8/24 17:00",
            "2009/8/24 18:00",
            "2009/8/24 19:00",
            "2009/8/24 20:00",
            "2009/8/24 21:00",
            "2009/8/24 22:00",
            "2009/8/24 23:00",
            "2009/8/25 0:00",
            "2009/8/25 1:00",
            "2009/8/25 2:00",
            "2009/8/25 3:00",
            "2009/8/25 4:00",
            "2009/8/25 5:00",
            "2009/8/25 6:00",
            "2009/8/25 7:00",
            "2009/8/25 8:00",
            "2009/8/25 9:00",
            "2009/8/25 10:00",
            "2009/8/25 11:00",
            "2009/8/25 12:00",
            "2009/8/25 13:00",
            "2009/8/25 14:00",
            "2009/8/25 15:00",
            "2009/8/25 16:00",
            "2009/8/25 17:00",
            "2009/8/25 18:00",
            "2009/8/25 19:00",
            "2009/8/25 20:00",
            "2009/8/25 21:00",
            "2009/8/25 22:00",
            "2009/8/25 23:00",
            "2009/8/26 0:00",
            "2009/8/26 1:00",
            "2009/8/26 2:00",
            "2009/8/26 3:00",
            "2009/8/26 4:00",
            "2009/8/26 5:00",
            "2009/8/26 6:00",
            "2009/8/26 7:00",
            "2009/8/26 8:00",
            "2009/8/26 9:00",
            "2009/8/26 10:00",
            "2009/8/26 11:00",
            "2009/8/26 12:00",
            "2009/8/26 13:00",
            "2009/8/26 14:00",
            "2009/8/26 15:00",
            "2009/8/26 16:00",
            "2009/8/26 17:00",
            "2009/8/26 18:00",
            "2009/8/26 19:00",
            "2009/8/26 20:00",
            "2009/8/26 21:00",
            "2009/8/26 22:00",
            "2009/8/26 23:00",
            "2009/8/27 0:00",
            "2009/8/27 1:00",
            "2009/8/27 2:00",
            "2009/8/27 3:00",
            "2009/8/27 4:00",
            "2009/8/27 5:00",
            "2009/8/27 6:00",
            "2009/8/27 7:00",
            "2009/8/27 8:00",
            "2009/8/27 9:00",
            "2009/8/27 10:00",
            "2009/8/27 11:00",
            "2009/8/27 12:00",
            "2009/8/27 13:00",
            "2009/8/27 14:00",
            "2009/8/27 15:00",
            "2009/8/27 16:00",
            "2009/8/27 17:00",
            "2009/8/27 18:00",
            "2009/8/27 19:00",
            "2009/8/27 20:00",
            "2009/8/27 21:00",
            "2009/8/27 22:00",
            "2009/8/27 23:00",
            "2009/8/28 0:00",
            "2009/8/28 1:00",
            "2009/8/28 2:00",
            "2009/8/28 3:00",
            "2009/8/28 4:00",
            "2009/8/28 5:00",
            "2009/8/28 6:00",
            "2009/8/28 7:00",
            "2009/8/28 8:00",
            "2009/8/28 9:00",
            "2009/8/28 10:00",
            "2009/8/28 11:00",
            "2009/8/28 12:00",
            "2009/8/28 13:00",
            "2009/8/28 14:00",
            "2009/8/28 15:00",
            "2009/8/28 16:00",
            "2009/8/28 17:00",
            "2009/8/28 18:00",
            "2009/8/28 19:00",
            "2009/8/28 20:00",
            "2009/8/28 21:00",
            "2009/8/28 22:00",
            "2009/8/28 23:00",
            "2009/8/29 0:00",
            "2009/8/29 1:00",
            "2009/8/29 2:00",
            "2009/8/29 3:00",
            "2009/8/29 4:00",
            "2009/8/29 5:00",
            "2009/8/29 6:00",
            "2009/8/29 7:00",
            "2009/8/29 8:00",
            "2009/8/29 9:00",
            "2009/8/29 10:00",
            "2009/8/29 11:00",
            "2009/8/29 12:00",
            "2009/8/29 13:00",
            "2009/8/29 14:00",
            "2009/8/29 15:00",
            "2009/8/29 16:00",
            "2009/8/29 17:00",
            "2009/8/29 18:00",
            "2009/8/29 19:00",
            "2009/8/29 20:00",
            "2009/8/29 21:00",
            "2009/8/29 22:00",
            "2009/8/29 23:00",
            "2009/8/30 0:00",
            "2009/8/30 1:00",
            "2009/8/30 2:00",
            "2009/8/30 3:00",
            "2009/8/30 4:00",
            "2009/8/30 5:00",
            "2009/8/30 6:00",
            "2009/8/30 7:00",
            "2009/8/30 8:00",
            "2009/8/30 9:00",
            "2009/8/30 10:00",
            "2009/8/30 11:00",
            "2009/8/30 12:00",
            "2009/8/30 13:00",
            "2009/8/30 14:00",
            "2009/8/30 15:00",
            "2009/8/30 16:00",
            "2009/8/30 17:00",
            "2009/8/30 18:00",
            "2009/8/30 19:00",
            "2009/8/30 20:00",
            "2009/8/30 21:00",
            "2009/8/30 22:00",
            "2009/8/30 23:00",
            "2009/8/31 0:00",
            "2009/8/31 1:00",
            "2009/8/31 2:00",
            "2009/8/31 3:00",
            "2009/8/31 4:00",
            "2009/8/31 5:00",
            "2009/8/31 6:00",
            "2009/8/31 7:00",
            "2009/8/31 8:00",
            "2009/8/31 9:00",
            "2009/8/31 10:00",
            "2009/8/31 11:00",
            "2009/8/31 12:00",
            "2009/8/31 13:00",
            "2009/8/31 14:00",
            "2009/8/31 15:00",
            "2009/8/31 16:00",
            "2009/8/31 17:00",
            "2009/8/31 18:00",
            "2009/8/31 19:00",
            "2009/8/31 20:00",
            "2009/8/31 21:00",
            "2009/8/31 22:00",
            "2009/8/31 23:00",
            "2009/9/1 0:00",
            "2009/9/1 1:00",
            "2009/9/1 2:00",
            "2009/9/1 3:00",
            "2009/9/1 4:00",
            "2009/9/1 5:00",
            "2009/9/1 6:00",
            "2009/9/1 7:00",
            "2009/9/1 8:00",
            "2009/9/1 9:00",
            "2009/9/1 10:00",
            "2009/9/1 11:00",
            "2009/9/1 12:00",
            "2009/9/1 13:00",
            "2009/9/1 14:00",
            "2009/9/1 15:00",
            "2009/9/1 16:00",
            "2009/9/1 17:00",
            "2009/9/1 18:00",
            "2009/9/1 19:00",
            "2009/9/1 20:00",
            "2009/9/1 21:00",
            "2009/9/1 22:00",
            "2009/9/1 23:00",
            "2009/9/2 0:00",
            "2009/9/2 1:00",
            "2009/9/2 2:00",
            "2009/9/2 3:00",
            "2009/9/2 4:00",
            "2009/9/2 5:00",
            "2009/9/2 6:00",
            "2009/9/2 7:00",
            "2009/9/2 8:00",
            "2009/9/2 9:00",
            "2009/9/2 10:00",
            "2009/9/2 11:00",
            "2009/9/2 12:00",
            "2009/9/2 13:00",
            "2009/9/2 14:00",
            "2009/9/2 15:00",
            "2009/9/2 16:00",
            "2009/9/2 17:00",
            "2009/9/2 18:00",
            "2009/9/2 19:00",
            "2009/9/2 20:00",
            "2009/9/2 21:00",
            "2009/9/2 22:00",
            "2009/9/2 23:00",
            "2009/9/3 0:00",
            "2009/9/3 1:00",
            "2009/9/3 2:00",
            "2009/9/3 3:00",
            "2009/9/3 4:00",
            "2009/9/3 5:00",
            "2009/9/3 6:00",
            "2009/9/3 7:00",
            "2009/9/3 8:00",
            "2009/9/3 9:00",
            "2009/9/3 10:00",
            "2009/9/3 11:00",
            "2009/9/3 12:00",
            "2009/9/3 13:00",
            "2009/9/3 14:00",
            "2009/9/3 15:00",
            "2009/9/3 16:00",
            "2009/9/3 17:00",
            "2009/9/3 18:00",
            "2009/9/3 19:00",
            "2009/9/3 20:00",
            "2009/9/3 21:00",
            "2009/9/3 22:00",
            "2009/9/3 23:00",
            "2009/9/4 0:00",
            "2009/9/4 1:00",
            "2009/9/4 2:00",
            "2009/9/4 3:00",
            "2009/9/4 4:00",
            "2009/9/4 5:00",
            "2009/9/4 6:00",
            "2009/9/4 7:00",
            "2009/9/4 8:00",
            "2009/9/4 9:00",
            "2009/9/4 10:00",
            "2009/9/4 11:00",
            "2009/9/4 12:00",
            "2009/9/4 13:00",
            "2009/9/4 14:00",
            "2009/9/4 15:00",
            "2009/9/4 16:00",
            "2009/9/4 17:00",
            "2009/9/4 18:00",
            "2009/9/4 19:00",
            "2009/9/4 20:00",
            "2009/9/4 21:00",
            "2009/9/4 22:00",
            "2009/9/4 23:00",
            "2009/9/5 0:00",
            "2009/9/5 1:00",
            "2009/9/5 2:00",
            "2009/9/5 3:00",
            "2009/9/5 4:00",
            "2009/9/5 5:00",
            "2009/9/5 6:00",
            "2009/9/5 7:00",
            "2009/9/5 8:00",
            "2009/9/5 9:00",
            "2009/9/5 10:00",
            "2009/9/5 11:00",
            "2009/9/5 12:00",
            "2009/9/5 13:00",
            "2009/9/5 14:00",
            "2009/9/5 15:00",
            "2009/9/5 16:00",
            "2009/9/5 17:00",
            "2009/9/5 18:00",
            "2009/9/5 19:00",
            "2009/9/5 20:00",
            "2009/9/5 21:00",
            "2009/9/5 22:00",
            "2009/9/5 23:00",
            "2009/9/6 0:00",
            "2009/9/6 1:00",
            "2009/9/6 2:00",
            "2009/9/6 3:00",
            "2009/9/6 4:00",
            "2009/9/6 5:00",
            "2009/9/6 6:00",
            "2009/9/6 7:00",
            "2009/9/6 8:00",
            "2009/9/6 9:00",
            "2009/9/6 10:00",
            "2009/9/6 11:00",
            "2009/9/6 12:00",
            "2009/9/6 13:00",
            "2009/9/6 14:00",
            "2009/9/6 15:00",
            "2009/9/6 16:00",
            "2009/9/6 17:00",
            "2009/9/6 18:00",
            "2009/9/6 19:00",
            "2009/9/6 20:00",
            "2009/9/6 21:00",
            "2009/9/6 22:00",
            "2009/9/6 23:00",
            "2009/9/7 0:00",
            "2009/9/7 1:00",
            "2009/9/7 2:00",
            "2009/9/7 3:00",
            "2009/9/7 4:00",
            "2009/9/7 5:00",
            "2009/9/7 6:00",
            "2009/9/7 7:00",
            "2009/9/7 8:00",
            "2009/9/7 9:00",
            "2009/9/7 10:00",
            "2009/9/7 11:00",
            "2009/9/7 12:00",
            "2009/9/7 13:00",
            "2009/9/7 14:00",
            "2009/9/7 15:00",
            "2009/9/7 16:00",
            "2009/9/7 17:00",
            "2009/9/7 18:00",
            "2009/9/7 19:00",
            "2009/9/7 20:00",
            "2009/9/7 21:00",
            "2009/9/7 22:00",
            "2009/9/7 23:00",
            "2009/9/8 0:00",
            "2009/9/8 1:00",
            "2009/9/8 2:00",
            "2009/9/8 3:00",
            "2009/9/8 4:00",
            "2009/9/8 5:00",
            "2009/9/8 6:00",
            "2009/9/8 7:00",
            "2009/9/8 8:00",
            "2009/9/8 9:00",
            "2009/9/8 10:00",
            "2009/9/8 11:00",
            "2009/9/8 12:00",
            "2009/9/8 13:00",
            "2009/9/8 14:00",
            "2009/9/8 15:00",
            "2009/9/8 16:00",
            "2009/9/8 17:00",
            "2009/9/8 18:00",
            "2009/9/8 19:00",
            "2009/9/8 20:00",
            "2009/9/8 21:00",
            "2009/9/8 22:00",
            "2009/9/8 23:00",
            "2009/9/9 0:00",
            "2009/9/9 1:00",
            "2009/9/9 2:00",
            "2009/9/9 3:00",
            "2009/9/9 4:00",
            "2009/9/9 5:00",
            "2009/9/9 6:00",
            "2009/9/9 7:00",
            "2009/9/9 8:00",
            "2009/9/9 9:00",
            "2009/9/9 10:00",
            "2009/9/9 11:00",
            "2009/9/9 12:00",
            "2009/9/9 13:00",
            "2009/9/9 14:00",
            "2009/9/9 15:00",
            "2009/9/9 16:00",
            "2009/9/9 17:00",
            "2009/9/9 18:00",
            "2009/9/9 19:00",
            "2009/9/9 20:00",
            "2009/9/9 21:00",
            "2009/9/9 22:00",
            "2009/9/9 23:00",
            "2009/9/10 0:00",
            "2009/9/10 1:00",
            "2009/9/10 2:00",
            "2009/9/10 3:00",
            "2009/9/10 4:00",
            "2009/9/10 5:00",
            "2009/9/10 6:00",
            "2009/9/10 7:00",
            "2009/9/10 8:00",
            "2009/9/10 9:00",
            "2009/9/10 10:00",
            "2009/9/10 11:00",
            "2009/9/10 12:00",
            "2009/9/10 13:00",
            "2009/9/10 14:00",
            "2009/9/10 15:00",
            "2009/9/10 16:00",
            "2009/9/10 17:00",
            "2009/9/10 18:00",
            "2009/9/10 19:00",
            "2009/9/10 20:00",
            "2009/9/10 21:00",
            "2009/9/10 22:00",
            "2009/9/10 23:00",
            "2009/9/11 0:00",
            "2009/9/11 1:00",
            "2009/9/11 2:00",
            "2009/9/11 3:00",
            "2009/9/11 4:00",
            "2009/9/11 5:00",
            "2009/9/11 6:00",
            "2009/9/11 7:00",
            "2009/9/11 8:00",
            "2009/9/11 9:00",
            "2009/9/11 10:00",
            "2009/9/11 11:00",
            "2009/9/11 12:00",
            "2009/9/11 13:00",
            "2009/9/11 14:00",
            "2009/9/11 15:00",
            "2009/9/11 16:00",
            "2009/9/11 17:00",
            "2009/9/11 18:00",
            "2009/9/11 19:00",
            "2009/9/11 20:00",
            "2009/9/11 21:00",
            "2009/9/11 22:00",
            "2009/9/11 23:00",
            "2009/9/12 0:00",
            "2009/9/12 1:00",
            "2009/9/12 2:00",
            "2009/9/12 3:00",
            "2009/9/12 4:00",
            "2009/9/12 5:00",
            "2009/9/12 6:00",
            "2009/9/12 7:00",
            "2009/9/12 8:00",
            "2009/9/12 9:00",
            "2009/9/12 10:00",
            "2009/9/12 11:00",
            "2009/9/12 12:00",
            "2009/9/12 13:00",
            "2009/9/12 14:00",
            "2009/9/12 15:00",
            "2009/9/12 16:00",
            "2009/9/12 17:00",
            "2009/9/12 18:00",
            "2009/9/12 19:00",
            "2009/9/12 20:00",
            "2009/9/12 21:00",
            "2009/9/12 22:00",
            "2009/9/12 23:00",
            "2009/9/13 0:00",
            "2009/9/13 1:00",
            "2009/9/13 2:00",
            "2009/9/13 3:00",
            "2009/9/13 4:00",
            "2009/9/13 5:00",
            "2009/9/13 6:00",
            "2009/9/13 7:00",
            "2009/9/13 8:00",
            "2009/9/13 9:00",
            "2009/9/13 10:00",
            "2009/9/13 11:00",
            "2009/9/13 12:00",
            "2009/9/13 13:00",
            "2009/9/13 14:00",
            "2009/9/13 15:00",
            "2009/9/13 16:00",
            "2009/9/13 17:00",
            "2009/9/13 18:00",
            "2009/9/13 19:00",
            "2009/9/13 20:00",
            "2009/9/13 21:00",
            "2009/9/13 22:00",
            "2009/9/13 23:00",
            "2009/9/14 0:00",
            "2009/9/14 1:00",
            "2009/9/14 2:00",
            "2009/9/14 3:00",
            "2009/9/14 4:00",
            "2009/9/14 5:00",
            "2009/9/14 6:00",
            "2009/9/14 7:00",
            "2009/9/14 8:00",
            "2009/9/14 9:00",
            "2009/9/14 10:00",
            "2009/9/14 11:00",
            "2009/9/14 12:00",
            "2009/9/14 13:00",
            "2009/9/14 14:00",
            "2009/9/14 15:00",
            "2009/9/14 16:00",
            "2009/9/14 17:00",
            "2009/9/14 18:00",
            "2009/9/14 19:00",
            "2009/9/14 20:00",
            "2009/9/14 21:00",
            "2009/9/14 22:00",
            "2009/9/14 23:00",
            "2009/9/15 0:00",
            "2009/9/15 1:00",
            "2009/9/15 2:00",
            "2009/9/15 3:00",
            "2009/9/15 4:00",
            "2009/9/15 5:00",
            "2009/9/15 6:00",
            "2009/9/15 7:00",
            "2009/9/15 8:00",
            "2009/9/15 9:00",
            "2009/9/15 10:00",
            "2009/9/15 11:00",
            "2009/9/15 12:00",
            "2009/9/15 13:00",
            "2009/9/15 14:00",
            "2009/9/15 15:00",
            "2009/9/15 16:00",
            "2009/9/15 17:00",
            "2009/9/15 18:00",
            "2009/9/15 19:00",
            "2009/9/15 20:00",
            "2009/9/15 21:00",
            "2009/9/15 22:00",
            "2009/9/15 23:00",
            "2009/9/16 0:00",
            "2009/9/16 1:00",
            "2009/9/16 2:00",
            "2009/9/16 3:00",
            "2009/9/16 4:00",
            "2009/9/16 5:00",
            "2009/9/16 6:00",
            "2009/9/16 7:00",
            "2009/9/16 8:00",
            "2009/9/16 9:00",
            "2009/9/16 10:00",
            "2009/9/16 11:00",
            "2009/9/16 12:00",
            "2009/9/16 13:00",
            "2009/9/16 14:00",
            "2009/9/16 15:00",
            "2009/9/16 16:00",
            "2009/9/16 17:00",
            "2009/9/16 18:00",
            "2009/9/16 19:00",
            "2009/9/16 20:00",
            "2009/9/16 21:00",
            "2009/9/16 22:00",
            "2009/9/16 23:00",
            "2009/9/17 0:00",
            "2009/9/17 1:00",
            "2009/9/17 2:00",
            "2009/9/17 3:00",
            "2009/9/17 4:00",
            "2009/9/17 5:00",
            "2009/9/17 6:00",
            "2009/9/17 7:00",
            "2009/9/17 8:00",
            "2009/9/17 9:00",
            "2009/9/17 10:00",
            "2009/9/17 11:00",
            "2009/9/17 12:00",
            "2009/9/17 13:00",
            "2009/9/17 14:00",
            "2009/9/17 15:00",
            "2009/9/17 16:00",
            "2009/9/17 17:00",
            "2009/9/17 18:00",
            "2009/9/17 19:00",
            "2009/9/17 20:00",
            "2009/9/17 21:00",
            "2009/9/17 22:00",
            "2009/9/17 23:00",
            "2009/9/18 0:00",
            "2009/9/18 1:00",
            "2009/9/18 2:00",
            "2009/9/18 3:00",
            "2009/9/18 4:00",
            "2009/9/18 5:00",
            "2009/9/18 6:00",
            "2009/9/18 7:00",
            "2009/9/18 8:00",
            "2009/9/18 9:00",
            "2009/9/18 10:00",
            "2009/9/18 11:00",
            "2009/9/18 12:00",
            "2009/9/18 13:00",
            "2009/9/18 14:00",
            "2009/9/18 15:00",
            "2009/9/18 16:00",
            "2009/9/18 17:00",
            "2009/9/18 18:00",
            "2009/9/18 19:00",
            "2009/9/18 20:00",
            "2009/9/18 21:00",
            "2009/9/18 22:00",
            "2009/9/18 23:00",
            "2009/9/19 0:00",
            "2009/9/19 1:00",
            "2009/9/19 2:00",
            "2009/9/19 3:00",
            "2009/9/19 4:00",
            "2009/9/19 5:00",
            "2009/9/19 6:00",
            "2009/9/19 7:00",
            "2009/9/19 8:00",
            "2009/9/19 9:00",
            "2009/9/19 10:00",
            "2009/9/19 11:00",
            "2009/9/19 12:00",
            "2009/9/19 13:00",
            "2009/9/19 14:00",
            "2009/9/19 15:00",
            "2009/9/19 16:00",
            "2009/9/19 17:00",
            "2009/9/19 18:00",
            "2009/9/19 19:00",
            "2009/9/19 20:00",
            "2009/9/19 21:00",
            "2009/9/19 22:00",
            "2009/9/19 23:00",
            "2009/9/20 0:00",
            "2009/9/20 1:00",
            "2009/9/20 2:00",
            "2009/9/20 3:00",
            "2009/9/20 4:00",
            "2009/9/20 5:00",
            "2009/9/20 6:00",
            "2009/9/20 7:00",
            "2009/9/20 8:00",
            "2009/9/20 9:00",
            "2009/9/20 10:00",
            "2009/9/20 11:00",
            "2009/9/20 12:00",
            "2009/9/20 13:00",
            "2009/9/20 14:00",
            "2009/9/20 15:00",
            "2009/9/20 16:00",
            "2009/9/20 17:00",
            "2009/9/20 18:00",
            "2009/9/20 19:00",
            "2009/9/20 20:00",
            "2009/9/20 21:00",
            "2009/9/20 22:00",
            "2009/9/20 23:00",
            "2009/9/21 0:00",
            "2009/9/21 1:00",
            "2009/9/21 2:00",
            "2009/9/21 3:00",
            "2009/9/21 4:00",
            "2009/9/21 5:00",
            "2009/9/21 6:00",
            "2009/9/21 7:00",
            "2009/9/21 8:00",
            "2009/9/21 9:00",
            "2009/9/21 10:00",
            "2009/9/21 11:00",
            "2009/9/21 12:00",
            "2009/9/21 13:00",
            "2009/9/21 14:00",
            "2009/9/21 15:00",
            "2009/9/21 16:00",
            "2009/9/21 17:00",
            "2009/9/21 18:00",
            "2009/9/21 19:00",
            "2009/9/21 20:00",
            "2009/9/21 21:00",
            "2009/9/21 22:00",
            "2009/9/21 23:00",
            "2009/9/22 0:00",
            "2009/9/22 1:00",
            "2009/9/22 2:00",
            "2009/9/22 3:00",
            "2009/9/22 4:00",
            "2009/9/22 5:00",
            "2009/9/22 6:00",
            "2009/9/22 7:00",
            "2009/9/22 8:00",
            "2009/9/22 9:00",
            "2009/9/22 10:00",
            "2009/9/22 11:00",
            "2009/9/22 12:00",
            "2009/9/22 13:00",
            "2009/9/22 14:00",
            "2009/9/22 15:00",
            "2009/9/22 16:00",
            "2009/9/22 17:00",
            "2009/9/22 18:00",
            "2009/9/22 19:00",
            "2009/9/22 20:00",
            "2009/9/22 21:00",
            "2009/9/22 22:00",
            "2009/9/22 23:00",
            "2009/9/23 0:00",
            "2009/9/23 1:00",
            "2009/9/23 2:00",
            "2009/9/23 3:00",
            "2009/9/23 4:00",
            "2009/9/23 5:00",
            "2009/9/23 6:00",
            "2009/9/23 7:00",
            "2009/9/23 8:00",
            "2009/9/23 9:00",
            "2009/9/23 10:00",
            "2009/9/23 11:00",
            "2009/9/23 12:00",
            "2009/9/23 13:00",
            "2009/9/23 14:00",
            "2009/9/23 15:00",
            "2009/9/23 16:00",
            "2009/9/23 17:00",
            "2009/9/23 18:00",
            "2009/9/23 19:00",
            "2009/9/23 20:00",
            "2009/9/23 21:00",
            "2009/9/23 22:00",
            "2009/9/23 23:00",
            "2009/9/24 0:00",
            "2009/9/24 1:00",
            "2009/9/24 2:00",
            "2009/9/24 3:00",
            "2009/9/24 4:00",
            "2009/9/24 5:00",
            "2009/9/24 6:00",
            "2009/9/24 7:00",
            "2009/9/24 8:00",
            "2009/9/24 9:00",
            "2009/9/24 10:00",
            "2009/9/24 11:00",
            "2009/9/24 12:00",
            "2009/9/24 13:00",
            "2009/9/24 14:00",
            "2009/9/24 15:00",
            "2009/9/24 16:00",
            "2009/9/24 17:00",
            "2009/9/24 18:00",
            "2009/9/24 19:00",
            "2009/9/24 20:00",
            "2009/9/24 21:00",
            "2009/9/24 22:00",
            "2009/9/24 23:00",
            "2009/9/25 0:00",
            "2009/9/25 1:00",
            "2009/9/25 2:00",
            "2009/9/25 3:00",
            "2009/9/25 4:00",
            "2009/9/25 5:00",
            "2009/9/25 6:00",
            "2009/9/25 7:00",
            "2009/9/25 8:00",
            "2009/9/25 9:00",
            "2009/9/25 10:00",
            "2009/9/25 11:00",
            "2009/9/25 12:00",
            "2009/9/25 13:00",
            "2009/9/25 14:00",
            "2009/9/25 15:00",
            "2009/9/25 16:00",
            "2009/9/25 17:00",
            "2009/9/25 18:00",
            "2009/9/25 19:00",
            "2009/9/25 20:00",
            "2009/9/25 21:00",
            "2009/9/25 22:00",
            "2009/9/25 23:00",
            "2009/9/26 0:00",
            "2009/9/26 1:00",
            "2009/9/26 2:00",
            "2009/9/26 3:00",
            "2009/9/26 4:00",
            "2009/9/26 5:00",
            "2009/9/26 6:00",
            "2009/9/26 7:00",
            "2009/9/26 8:00",
            "2009/9/26 9:00",
            "2009/9/26 10:00",
            "2009/9/26 11:00",
            "2009/9/26 12:00",
            "2009/9/26 13:00",
            "2009/9/26 14:00",
            "2009/9/26 15:00",
            "2009/9/26 16:00",
            "2009/9/26 17:00",
            "2009/9/26 18:00",
            "2009/9/26 19:00",
            "2009/9/26 20:00",
            "2009/9/26 21:00",
            "2009/9/26 22:00",
            "2009/9/26 23:00",
            "2009/9/27 0:00",
            "2009/9/27 1:00",
            "2009/9/27 2:00",
            "2009/9/27 3:00",
            "2009/9/27 4:00",
            "2009/9/27 5:00",
            "2009/9/27 6:00",
            "2009/9/27 7:00",
            "2009/9/27 8:00",
            "2009/9/27 9:00",
            "2009/9/27 10:00",
            "2009/9/27 11:00",
            "2009/9/27 12:00",
            "2009/9/27 13:00",
            "2009/9/27 14:00",
            "2009/9/27 15:00",
            "2009/9/27 16:00",
            "2009/9/27 17:00",
            "2009/9/27 18:00",
            "2009/9/27 19:00",
            "2009/9/27 20:00",
            "2009/9/27 21:00",
            "2009/9/27 22:00",
            "2009/9/27 23:00",
            "2009/9/28 0:00",
            "2009/9/28 1:00",
            "2009/9/28 2:00",
            "2009/9/28 3:00",
            "2009/9/28 4:00",
            "2009/9/28 5:00",
            "2009/9/28 6:00",
            "2009/9/28 7:00",
            "2009/9/28 8:00",
            "2009/9/28 9:00",
            "2009/9/28 10:00",
            "2009/9/28 11:00",
            "2009/9/28 12:00",
            "2009/9/28 13:00",
            "2009/9/28 14:00",
            "2009/9/28 15:00",
            "2009/9/28 16:00",
            "2009/9/28 17:00",
            "2009/9/28 18:00",
            "2009/9/28 19:00",
            "2009/9/28 20:00",
            "2009/9/28 21:00",
            "2009/9/28 22:00",
            "2009/9/28 23:00",
            "2009/9/29 0:00",
            "2009/9/29 1:00",
            "2009/9/29 2:00",
            "2009/9/29 3:00",
            "2009/9/29 4:00",
            "2009/9/29 5:00",
            "2009/9/29 6:00",
            "2009/9/29 7:00",
            "2009/9/29 8:00",
            "2009/9/29 9:00",
            "2009/9/29 10:00",
            "2009/9/29 11:00",
            "2009/9/29 12:00",
            "2009/9/29 13:00",
            "2009/9/29 14:00",
            "2009/9/29 15:00",
            "2009/9/29 16:00",
            "2009/9/29 17:00",
            "2009/9/29 18:00",
            "2009/9/29 19:00",
            "2009/9/29 20:00",
            "2009/9/29 21:00",
            "2009/9/29 22:00",
            "2009/9/29 23:00",
            "2009/9/30 0:00",
            "2009/9/30 1:00",
            "2009/9/30 2:00",
            "2009/9/30 3:00",
            "2009/9/30 4:00",
            "2009/9/30 5:00",
            "2009/9/30 6:00",
            "2009/9/30 7:00",
            "2009/9/30 8:00",
            "2009/9/30 9:00",
            "2009/9/30 10:00",
            "2009/9/30 11:00",
            "2009/9/30 12:00",
            "2009/9/30 13:00",
            "2009/9/30 14:00",
            "2009/9/30 15:00",
            "2009/9/30 16:00",
            "2009/9/30 17:00",
            "2009/9/30 18:00",
            "2009/9/30 19:00",
            "2009/9/30 20:00",
            "2009/9/30 21:00",
            "2009/9/30 22:00",
            "2009/9/30 23:00",
            "2009/10/1 0:00",
            "2009/10/1 1:00",
            "2009/10/1 2:00",
            "2009/10/1 3:00",
            "2009/10/1 4:00",
            "2009/10/1 5:00",
            "2009/10/1 6:00",
            "2009/10/1 7:00",
            "2009/10/1 8:00",
            "2009/10/1 9:00",
            "2009/10/1 10:00",
            "2009/10/1 11:00",
            "2009/10/1 12:00",
            "2009/10/1 13:00",
            "2009/10/1 14:00",
            "2009/10/1 15:00",
            "2009/10/1 16:00",
            "2009/10/1 17:00",
            "2009/10/1 18:00",
            "2009/10/1 19:00",
            "2009/10/1 20:00",
            "2009/10/1 21:00",
            "2009/10/1 22:00",
            "2009/10/1 23:00",
            "2009/10/2 0:00",
            "2009/10/2 1:00",
            "2009/10/2 2:00",
            "2009/10/2 3:00",
            "2009/10/2 4:00",
            "2009/10/2 5:00",
            "2009/10/2 6:00",
            "2009/10/2 7:00",
            "2009/10/2 8:00",
            "2009/10/2 9:00",
            "2009/10/2 10:00",
            "2009/10/2 11:00",
            "2009/10/2 12:00",
            "2009/10/2 13:00",
            "2009/10/2 14:00",
            "2009/10/2 15:00",
            "2009/10/2 16:00",
            "2009/10/2 17:00",
            "2009/10/2 18:00",
            "2009/10/2 19:00",
            "2009/10/2 20:00",
            "2009/10/2 21:00",
            "2009/10/2 22:00",
            "2009/10/2 23:00",
            "2009/10/3 0:00",
            "2009/10/3 1:00",
            "2009/10/3 2:00",
            "2009/10/3 3:00",
            "2009/10/3 4:00",
            "2009/10/3 5:00",
            "2009/10/3 6:00",
            "2009/10/3 7:00",
            "2009/10/3 8:00",
            "2009/10/3 9:00",
            "2009/10/3 10:00",
            "2009/10/3 11:00",
            "2009/10/3 12:00",
            "2009/10/3 13:00",
            "2009/10/3 14:00",
            "2009/10/3 15:00",
            "2009/10/3 16:00",
            "2009/10/3 17:00",
            "2009/10/3 18:00",
            "2009/10/3 19:00",
            "2009/10/3 20:00",
            "2009/10/3 21:00",
            "2009/10/3 22:00",
            "2009/10/3 23:00",
            "2009/10/4 0:00",
            "2009/10/4 1:00",
            "2009/10/4 2:00",
            "2009/10/4 3:00",
            "2009/10/4 4:00",
            "2009/10/4 5:00",
            "2009/10/4 6:00",
            "2009/10/4 7:00",
            "2009/10/4 8:00",
            "2009/10/4 9:00",
            "2009/10/4 10:00",
            "2009/10/4 11:00",
            "2009/10/4 12:00",
            "2009/10/4 13:00",
            "2009/10/4 14:00",
            "2009/10/4 15:00",
            "2009/10/4 16:00",
            "2009/10/4 17:00",
            "2009/10/4 18:00",
            "2009/10/4 19:00",
            "2009/10/4 20:00",
            "2009/10/4 21:00",
            "2009/10/4 22:00",
            "2009/10/4 23:00",
            "2009/10/5 0:00",
            "2009/10/5 1:00",
            "2009/10/5 2:00",
            "2009/10/5 3:00",
            "2009/10/5 4:00",
            "2009/10/5 5:00",
            "2009/10/5 6:00",
            "2009/10/5 7:00",
            "2009/10/5 8:00",
            "2009/10/5 9:00",
            "2009/10/5 10:00",
            "2009/10/5 11:00",
            "2009/10/5 12:00",
            "2009/10/5 13:00",
            "2009/10/5 14:00",
            "2009/10/5 15:00",
            "2009/10/5 16:00",
            "2009/10/5 17:00",
            "2009/10/5 18:00",
            "2009/10/5 19:00",
            "2009/10/5 20:00",
            "2009/10/5 21:00",
            "2009/10/5 22:00",
            "2009/10/5 23:00",
            "2009/10/6 0:00",
            "2009/10/6 1:00",
            "2009/10/6 2:00",
            "2009/10/6 3:00",
            "2009/10/6 4:00",
            "2009/10/6 5:00",
            "2009/10/6 6:00",
            "2009/10/6 7:00",
            "2009/10/6 8:00",
            "2009/10/6 9:00",
            "2009/10/6 10:00",
            "2009/10/6 11:00",
            "2009/10/6 12:00",
            "2009/10/6 13:00",
            "2009/10/6 14:00",
            "2009/10/6 15:00",
            "2009/10/6 16:00",
            "2009/10/6 17:00",
            "2009/10/6 18:00",
            "2009/10/6 19:00",
            "2009/10/6 20:00",
            "2009/10/6 21:00",
            "2009/10/6 22:00",
            "2009/10/6 23:00",
            "2009/10/7 0:00",
            "2009/10/7 1:00",
            "2009/10/7 2:00",
            "2009/10/7 3:00",
            "2009/10/7 4:00",
            "2009/10/7 5:00",
            "2009/10/7 6:00",
            "2009/10/7 7:00",
            "2009/10/7 8:00",
            "2009/10/7 9:00",
            "2009/10/7 10:00",
            "2009/10/7 11:00",
            "2009/10/7 12:00",
            "2009/10/7 13:00",
            "2009/10/7 14:00",
            "2009/10/7 15:00",
            "2009/10/7 16:00",
            "2009/10/7 17:00",
            "2009/10/7 18:00",
            "2009/10/7 19:00",
            "2009/10/7 20:00",
            "2009/10/7 21:00",
            "2009/10/7 22:00",
            "2009/10/7 23:00",
            "2009/10/8 0:00",
            "2009/10/8 1:00",
            "2009/10/8 2:00",
            "2009/10/8 3:00",
            "2009/10/8 4:00",
            "2009/10/8 5:00",
            "2009/10/8 6:00",
            "2009/10/8 7:00",
            "2009/10/8 8:00",
            "2009/10/8 9:00",
            "2009/10/8 10:00",
            "2009/10/8 11:00",
            "2009/10/8 12:00",
            "2009/10/8 13:00",
            "2009/10/8 14:00",
            "2009/10/8 15:00",
            "2009/10/8 16:00",
            "2009/10/8 17:00",
            "2009/10/8 18:00",
            "2009/10/8 19:00",
            "2009/10/8 20:00",
            "2009/10/8 21:00",
            "2009/10/8 22:00",
            "2009/10/8 23:00",
            "2009/10/9 0:00",
            "2009/10/9 1:00",
            "2009/10/9 2:00",
            "2009/10/9 3:00",
            "2009/10/9 4:00",
            "2009/10/9 5:00",
            "2009/10/9 6:00",
            "2009/10/9 7:00",
            "2009/10/9 8:00",
            "2009/10/9 9:00",
            "2009/10/9 10:00",
            "2009/10/9 11:00",
            "2009/10/9 12:00",
            "2009/10/9 13:00",
            "2009/10/9 14:00",
            "2009/10/9 15:00",
            "2009/10/9 16:00",
            "2009/10/9 17:00",
            "2009/10/9 18:00",
            "2009/10/9 19:00",
            "2009/10/9 20:00",
            "2009/10/9 21:00",
            "2009/10/9 22:00",
            "2009/10/9 23:00",
            "2009/10/10 0:00",
            "2009/10/10 1:00",
            "2009/10/10 2:00",
            "2009/10/10 3:00",
            "2009/10/10 4:00",
            "2009/10/10 5:00",
            "2009/10/10 6:00",
            "2009/10/10 7:00",
            "2009/10/10 8:00",
            "2009/10/10 9:00",
            "2009/10/10 10:00",
            "2009/10/10 11:00",
            "2009/10/10 12:00",
            "2009/10/10 13:00",
            "2009/10/10 14:00",
            "2009/10/10 15:00",
            "2009/10/10 16:00",
            "2009/10/10 17:00",
            "2009/10/10 18:00",
            "2009/10/10 19:00",
            "2009/10/10 20:00",
            "2009/10/10 21:00",
            "2009/10/10 22:00",
            "2009/10/10 23:00",
            "2009/10/11 0:00",
            "2009/10/11 1:00",
            "2009/10/11 2:00",
            "2009/10/11 3:00",
            "2009/10/11 4:00",
            "2009/10/11 5:00",
            "2009/10/11 6:00",
            "2009/10/11 7:00",
            "2009/10/11 8:00",
            "2009/10/11 9:00",
            "2009/10/11 10:00",
            "2009/10/11 11:00",
            "2009/10/11 12:00",
            "2009/10/11 13:00",
            "2009/10/11 14:00",
            "2009/10/11 15:00",
            "2009/10/11 16:00",
            "2009/10/11 17:00",
            "2009/10/11 18:00",
            "2009/10/11 19:00",
            "2009/10/11 20:00",
            "2009/10/11 21:00",
            "2009/10/11 22:00",
            "2009/10/11 23:00",
            "2009/10/12 0:00",
            "2009/10/12 1:00",
            "2009/10/12 2:00",
            "2009/10/12 3:00",
            "2009/10/12 4:00",
            "2009/10/12 5:00",
            "2009/10/12 6:00",
            "2009/10/12 7:00",
            "2009/10/12 8:00",
            "2009/10/12 9:00",
            "2009/10/12 10:00",
            "2009/10/12 11:00",
            "2009/10/12 12:00",
            "2009/10/12 13:00",
            "2009/10/12 14:00",
            "2009/10/12 15:00",
            "2009/10/12 16:00",
            "2009/10/12 17:00",
            "2009/10/12 18:00",
            "2009/10/12 19:00",
            "2009/10/12 20:00",
            "2009/10/12 21:00",
            "2009/10/12 22:00",
            "2009/10/12 23:00",
            "2009/10/13 0:00",
            "2009/10/13 1:00",
            "2009/10/13 2:00",
            "2009/10/13 3:00",
            "2009/10/13 4:00",
            "2009/10/13 5:00",
            "2009/10/13 6:00",
            "2009/10/13 7:00",
            "2009/10/13 8:00",
            "2009/10/13 9:00",
            "2009/10/13 10:00",
            "2009/10/13 11:00",
            "2009/10/13 12:00",
            "2009/10/13 13:00",
            "2009/10/13 14:00",
            "2009/10/13 15:00",
            "2009/10/13 16:00",
            "2009/10/13 17:00",
            "2009/10/13 18:00",
            "2009/10/13 19:00",
            "2009/10/13 20:00",
            "2009/10/13 21:00",
            "2009/10/13 22:00",
            "2009/10/13 23:00",
            "2009/10/14 0:00",
            "2009/10/14 1:00",
            "2009/10/14 2:00",
            "2009/10/14 3:00",
            "2009/10/14 4:00",
            "2009/10/14 5:00",
            "2009/10/14 6:00",
            "2009/10/14 7:00",
            "2009/10/14 8:00",
            "2009/10/14 9:00",
            "2009/10/14 10:00",
            "2009/10/14 11:00",
            "2009/10/14 12:00",
            "2009/10/14 13:00",
            "2009/10/14 14:00",
            "2009/10/14 15:00",
            "2009/10/14 16:00",
            "2009/10/14 17:00",
            "2009/10/14 18:00",
            "2009/10/14 19:00",
            "2009/10/14 20:00",
            "2009/10/14 21:00",
            "2009/10/14 22:00",
            "2009/10/14 23:00",
            "2009/10/15 0:00",
            "2009/10/15 1:00",
            "2009/10/15 2:00",
            "2009/10/15 3:00",
            "2009/10/15 4:00",
            "2009/10/15 5:00",
            "2009/10/15 6:00",
            "2009/10/15 7:00",
            "2009/10/15 8:00",
            "2009/10/15 9:00",
            "2009/10/15 10:00",
            "2009/10/15 11:00",
            "2009/10/15 12:00",
            "2009/10/15 13:00",
            "2009/10/15 14:00",
            "2009/10/15 15:00",
            "2009/10/15 16:00",
            "2009/10/15 17:00",
            "2009/10/15 18:00",
            "2009/10/15 19:00",
            "2009/10/15 20:00",
            "2009/10/15 21:00",
            "2009/10/15 22:00",
            "2009/10/15 23:00",
            "2009/10/16 0:00",
            "2009/10/16 1:00",
            "2009/10/16 2:00",
            "2009/10/16 3:00",
            "2009/10/16 4:00",
            "2009/10/16 5:00",
            "2009/10/16 6:00",
            "2009/10/16 7:00",
            "2009/10/16 8:00",
            "2009/10/16 9:00",
            "2009/10/16 10:00",
            "2009/10/16 11:00",
            "2009/10/16 12:00",
            "2009/10/16 13:00",
            "2009/10/16 14:00",
            "2009/10/16 15:00",
            "2009/10/16 16:00",
            "2009/10/16 17:00",
            "2009/10/16 18:00",
            "2009/10/16 19:00",
            "2009/10/16 20:00",
            "2009/10/16 21:00",
            "2009/10/16 22:00",
            "2009/10/16 23:00",
            "2009/10/17 0:00",
            "2009/10/17 1:00",
            "2009/10/17 2:00",
            "2009/10/17 3:00",
            "2009/10/17 4:00",
            "2009/10/17 5:00",
            "2009/10/17 6:00",
            "2009/10/17 7:00",
            "2009/10/17 8:00",
            "2009/10/17 9:00",
            "2009/10/17 10:00",
            "2009/10/17 11:00",
            "2009/10/17 12:00",
            "2009/10/17 13:00",
            "2009/10/17 14:00",
            "2009/10/17 15:00",
            "2009/10/17 16:00",
            "2009/10/17 17:00",
            "2009/10/17 18:00",
            "2009/10/17 19:00",
            "2009/10/17 20:00",
            "2009/10/17 21:00",
            "2009/10/17 22:00",
            "2009/10/17 23:00",
            "2009/10/18 0:00",
            "2009/10/18 1:00",
            "2009/10/18 2:00",
            "2009/10/18 3:00",
            "2009/10/18 4:00",
            "2009/10/18 5:00",
            "2009/10/18 6:00",
            "2009/10/18 7:00",
            "2009/10/18 8:00",
          ],
        },
      ],
      yAxis: [
        {
          name: "Flow(m^3/s)",
          type: "value",
          max: 500,
        },
        {
          name: "Rainfall(mm)",
          type: "value",
          inverse: true,
        },
      ],
      series: [
        {
          name: "Flow",
          type: "line",
          notShowSymbol: true,
          sampling: sampling,
          hoverAnimation: false,
          itemStyle: { normal: { areaStyle: { type: "default" } } },
          markArea: {
            silent: true,
            data: [
              [
                {
                  // name: 'a',
                  xAxis: "2009/10/2 7:00",
                  // xAxis: 1
                },
                {
                  // name: 'b',
                  xAxis: "2009/10/12 7:00",
                  // xAxis: 5
                },
              ],
            ],
          },
          data: [
            0.97, 0.96, 0.96, 0.95, 0.95, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.93, 0.92, 0.91,
            0.9, 0.89, 0.88, 0.87, 0.87, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.87, 0.88,
            0.9, 0.93, 0.96, 0.99, 1.03, 1.06, 1.1, 1.14, 1.17, 1.2, 1.23, 1.26,
            1.29, 1.33, 1.36, 1.4, 1.43, 1.45, 1.48, 1.49, 1.51, 1.51, 1.5,
            1.49, 1.47, 1.44, 1.41, 1.37, 1.34, 1.3, 1.27, 1.24, 1.22, 1.2,
            1.19, 1.18, 1.16, 1.15, 1.14, 1.13, 1.12, 1.11, 1.11, 1.1, 1.1, 1.1,
            1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1, 1.1,
            1.1, 1.09, 1.09, 1.08, 1.07, 1.06, 1.05, 1.04, 1.03, 1.03, 1.02,
            1.01, 1.01, 1, 0.99, 0.98, 0.97, 0.96, 0.96, 0.95, 0.95, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.93, 0.92, 0.91, 0.9, 0.89, 0.88,
            0.87, 0.87, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.85, 0.84, 0.83, 0.82, 0.81, 0.8, 0.8, 0.79, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.77, 0.75, 0.73, 0.71, 0.68, 0.65, 0.63, 0.61,
            0.59, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58,
            0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58,
            0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58,
            0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58,
            0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.57, 0.57, 0.57,
            0.56, 0.55, 0.55, 0.54, 0.54, 0.53, 0.52, 0.52, 0.51, 0.51, 0.5,
            0.5, 0.49, 0.48, 0.48, 0.47, 0.47, 0.47, 0.46, 0.46, 0.46, 0.46,
            0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46,
            0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46,
            0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46, 0.46,
            0.46, 0.52, 0.67, 0.9, 1.19, 1.52, 1.87, 2.22, 2.55, 2.84, 3.07,
            3.22, 3.28, 3.28, 3.28, 3.28, 3.28, 3.28, 3.28, 3.28, 3.28, 3.28,
            3.28, 3.28, 3.28, 3.24, 3.13, 2.97, 2.77, 2.54, 2.3, 2.05, 1.82,
            1.62, 1.46, 1.35, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31,
            1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31,
            1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31,
            1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.31, 1.3, 1.26, 1.21, 1.14,
            1.06, 0.97, 0.89, 0.81, 0.74, 0.69, 0.65, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.63, 0.63, 0.62, 0.62, 0.61, 0.6, 0.59, 0.59, 0.58, 0.58,
            0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58,
            0.58, 0.58, 0.58, 0.59, 0.61, 0.63, 0.65, 0.68, 0.71, 0.73, 0.75,
            0.77, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.77, 0.75, 0.73, 0.71, 0.68, 0.65,
            0.63, 0.61, 0.59, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58,
            0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.58, 0.59, 0.59, 0.6,
            0.61, 0.62, 0.62, 0.63, 0.63, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.65, 0.66,
            0.68, 0.69, 0.71, 0.73, 0.74, 0.76, 0.77, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78,
            0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.79, 0.81, 0.82,
            0.84, 0.86, 0.88, 0.9, 0.92, 0.93, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94, 0.94,
            0.93, 0.92, 0.91, 0.9, 0.89, 0.88, 0.87, 0.87, 0.86, 0.86, 0.86,
            0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86, 0.86,
            0.86, 0.85, 0.84, 0.82, 0.8, 0.78, 0.76, 0.75, 0.73, 0.72, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.72, 0.73, 0.74, 0.76, 0.78, 0.79,
            0.82, 0.84, 0.86, 0.89, 0.91, 0.94, 0.97, 1, 1.02, 1.05, 1.08, 1.11,
            1.14, 1.17, 1.19, 1.22, 1.25, 1.27, 1.29, 1.31, 1.33, 1.35, 1.36,
            1.38, 1.39, 1.39, 1.4, 1.4, 1.4, 1.39, 1.37, 1.35, 1.32, 1.29, 1.26,
            1.22, 1.18, 1.14, 1.1, 1.05, 1.01, 0.97, 0.93, 0.89, 0.85, 0.82,
            0.78, 0.76, 0.74, 0.72, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.72, 0.73,
            0.74, 0.75, 0.77, 0.78, 0.8, 0.82, 0.84, 0.87, 0.89, 0.92, 0.94,
            0.97, 0.99, 1.02, 1.05, 1.08, 1.1, 1.13, 1.16, 1.18, 1.21, 1.23,
            1.26, 1.28, 1.3, 1.32, 1.34, 1.35, 1.37, 1.38, 1.39, 1.4, 1.41,
            1.41, 1.42, 1.42, 1.43, 1.43, 1.43, 1.44, 1.44, 1.44, 1.44, 1.45,
            1.45, 1.45, 1.46, 1.46, 1.46, 1.47, 1.47, 1.48, 1.48, 1.49, 1.5,
            1.51, 1.54, 1.62, 1.73, 1.88, 2.05, 2.24, 2.45, 2.67, 2.89, 3.11,
            3.31, 3.51, 3.69, 3.86, 4.03, 4.18, 4.33, 4.48, 4.62, 4.76, 4.89,
            5.02, 5.16, 5.29, 5.43, 5.57, 5.71, 5.86, 6.02, 6.18, 6.36, 6.54,
            6.73, 6.93, 7.15, 7.38, 7.62, 7.88, 8.16, 8.46, 8.77, 9.11, 9.46,
            9.84, 10.24, 10.67, 11.12, 11.6, 12.3, 13.66, 16, 38.43, 82.21,
            146.6, 218.7, 226, 225.23, 223.08, 219.78, 212, 199.82, 184.6, 168,
            151.65, 137.21, 126.31, 119.94, 115.52, 112.06, 108.92, 105.44, 101,
            94.56, 86.36, 77.67, 69.76, 63.9, 60.38, 57.41, 54.84, 52.57, 50.56,
            48.71, 46.97, 45.25, 43.48, 41.6, 39.5, 37.19, 34.81, 32.46, 30.27,
            28.36, 26.85, 25.86, 25.5, 25.5, 25.5, 25.5, 25.5, 25.5, 25.5, 25.5,
            25.5, 25.5, 25.5, 25.5, 25.5, 25.27, 24.65, 23.7, 22.52, 21.17,
            19.75, 18.33, 16.98, 15.8, 14.85, 14.23, 14, 14.02, 14.08, 14.17,
            14.29, 14.44, 14.61, 14.8, 15.01, 15.23, 15.47, 15.71, 15.95, 16.19,
            16.43, 16.67, 16.89, 17.1, 17.29, 17.46, 17.61, 17.73, 17.82, 17.88,
            17.9, 17.63, 16.88, 15.75, 14.33, 12.71, 10.98, 9.23, 7.56, 6.05,
            4.81, 3.92, 3.47, 3.28, 3.1, 2.93, 2.76, 2.61, 2.46, 2.32, 2.19,
            2.07, 1.96, 1.85, 1.75, 1.66, 1.58, 1.51, 1.44, 1.39, 1.34, 1.29,
            1.26, 1.23, 1.22, 1.2, 1.2, 1.2, 1.2, 1.2, 1.2, 1.21, 1.21, 1.21,
            1.21, 1.22, 1.22, 1.22, 1.23, 1.23, 1.23, 1.24, 1.24, 1.25, 1.25,
            1.25, 1.26, 1.26, 1.27, 1.27, 1.27, 1.28, 1.28, 1.28, 1.29, 1.29,
            1.29, 1.29, 1.3, 1.3, 1.3, 1.3, 1.3, 1.3, 1.3, 1.3, 1.3, 1.3, 1.3,
            1.29, 1.29, 1.29, 1.29, 1.28, 1.28, 1.28, 1.27, 1.27, 1.26, 1.25,
            1.25, 1.24, 1.23, 1.23, 1.22, 1.21, 1.2, 1.16, 1.06, 0.95, 0.83,
            0.74, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71, 0.71,
            0.71, 0.71, 0.71, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7,
            0.7, 0.69, 0.69, 0.69, 0.69, 0.69, 0.69, 0.69, 0.69, 0.68, 0.68,
            0.68, 0.68, 0.68, 0.68, 0.67, 0.67, 0.67, 0.67, 0.67, 0.67, 0.67,
            0.66, 0.66, 0.66, 0.66, 0.66, 0.66, 0.66, 0.65, 0.65, 0.65, 0.65,
            0.65, 0.65, 0.65, 0.65, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.65, 0.66, 0.68, 0.69, 0.71, 0.73, 0.74,
            0.76, 0.77, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.78, 0.8,
            0.86, 0.95, 1.08, 1.25, 1.46, 1.7, 1.97, 2.28, 2.63, 3.01, 3.42,
            3.87, 4.35, 4.86, 5.4, 5.98, 6.59, 7.92, 10.49, 14.04, 18.31, 23.04,
            27.98, 32.87, 37.45, 41.46, 44.64, 46.74, 47.5, 46.86, 45.16, 42.77,
            40.04, 37.33, 35, 32.74, 30.21, 27.7, 25.5, 23.9, 23.2, 23.06,
            22.94, 22.84, 22.77, 22.72, 22.7, 22.8, 23.23, 23.95, 24.91, 26.04,
            27.3, 28.76, 30.7, 33.39, 37.12, 42.15, 48.77, 65.22, 252.1, 257,
            237.32, 221.19, 212, 208.67, 206.89, 205.2, 202.15, 189.82, 172,
            165.3, 160.49, 156.8, 153.44, 149.62, 144.6, 138.27, 131, 123.11,
            114.9, 106.69, 98.79, 91.5, 85.13, 80, 75.53, 71.03, 66.65, 62.54,
            58.85, 55.73, 53.31, 51.75, 51.2, 56.53, 68.25, 80, 91.01, 102.03,
            109, 112.37, 115.29, 117.68, 119.48, 120.61, 121, 119.45, 115.57,
            110.52, 105.47, 101.58, 100, 99.97, 99.94, 99.92, 99.9, 99.88,
            99.86, 99.85, 99.84, 99.83, 99.82, 99.81, 99.81, 99.8, 99.8, 99.8,
            122.15, 163.65, 186, 182.96, 175.15, 164.56, 153.18, 143, 136,
            131.37, 126.98, 122.81, 118.85, 115.09, 111.52, 108.13, 104.9,
            101.83, 98.9, 96.11, 93.44, 90.87, 88.41, 86.04, 83.74, 81.51,
            79.33, 77.2, 75.1, 73.02, 70.95, 68.88, 66.8, 64.87, 63.14, 61.4,
            59.53, 57.67, 56, 54.6, 53.36, 52.2, 51.05, 49.85, 48.5, 46.87,
            44.92, 42.74, 40.42, 38.04, 35.69, 33.46, 31.44, 29.72, 28.38,
            27.51, 27.2, 27.2, 27.2, 27.2, 27.2, 27.2, 27.2, 27.2, 27.2, 27.2,
            27.2, 27.2, 27.2, 27.14, 26.97, 26.7, 26.35, 25.95, 25.49, 25.02,
            24.53, 24.04, 23.58, 23.16, 22.8, 22.46, 22.11, 21.75, 21.39, 21.03,
            20.69, 20.36, 20.05, 19.78, 19.54, 19.35, 19.2, 19.09, 19, 18.92,
            18.85, 18.79, 18.74, 18.68, 18.62, 18.56, 18.49, 18.4, 18.3, 18.17,
            18.02, 17.83, 17.63, 17.41, 17.18, 16.93, 16.68, 16.43, 16.18,
            15.93, 15.7, 15.47, 15.22, 14.97, 14.71, 14.45, 14.18, 13.93, 13.68,
            13.44, 13.21, 13, 12.8, 12.62, 12.46, 12.31, 12.16, 12.03, 11.89,
            11.76, 11.62, 11.48, 11.33, 11.17, 11, 10.81, 10.59, 10.36, 10.12,
            9.86, 9.61, 9.36, 9.12, 8.89, 8.68, 8.5, 8.35, 8.21, 8.08, 7.94,
            7.81, 7.68, 7.56, 7.46, 7.36, 7.29, 7.23, 7.19, 7.18, 7.51, 8.42,
            9.81, 11.58, 13.63, 15.86, 18.16, 20.44, 22.58, 24.49, 26.06, 27.2,
            28.08, 28.95, 29.81, 30.65, 31.48, 32.28, 33.07, 33.82, 34.55,
            35.25, 35.92, 36.56, 37.15, 37.71, 38.23, 38.7, 39.13, 39.5, 39.83,
            40.1, 40.31, 40.47, 40.57, 40.6, 40.49, 40.16, 39.64, 38.94, 38.09,
            37.1, 36, 34.79, 33.51, 32.17, 30.79, 29.39, 27.99, 26.6, 25.25,
            23.96, 22.75, 21.63, 20.63, 19.76, 19.04, 18.49, 18.14, 18, 17.97,
            17.95, 17.94, 17.92, 17.91, 17.9, 17.89, 17.88, 17.87, 17.85, 17.83,
            17.8, 17.7, 17.46, 17.13, 16.7, 16.21, 15.68, 15.13, 14.57, 14.04,
            13.56, 13.14, 12.8, 12.52, 12.27, 12.02, 11.79, 11.57, 11.37, 11.16,
            10.97, 10.78, 10.59, 10.39, 10.2, 10.01, 9.81, 9.63, 9.44, 9.26,
            9.08, 8.9, 8.73, 8.56, 8.39, 8.22, 8.06, 7.9, 7.73, 7.57, 7.41,
            7.25, 7.09, 6.94, 6.79, 6.65, 6.52, 6.4, 6.28, 6.17, 6.08, 5.98,
            5.9, 5.81, 5.73, 5.65, 5.57, 5.49, 5.41, 5.32, 5.23, 5.14, 5.04,
            4.94, 4.84, 4.74, 4.63, 4.53, 4.43, 4.33, 4.23, 4.13, 4.03, 3.93,
            3.81, 3.69, 3.57, 3.45, 3.33, 3.22, 3.12, 3.04, 2.98, 2.93, 2.92,
            2.92, 2.92, 2.92, 2.92, 2.92, 2.92, 2.92, 2.92, 2.92, 2.92, 2.92,
            2.92, 2.9, 2.86, 2.8, 2.71, 2.62, 2.52, 2.42, 2.33, 2.24, 2.18,
            2.14, 2.12, 2.12, 2.12, 2.12, 2.12, 2.12, 2.12, 2.12, 2.12, 2.12,
            2.12, 2.12, 2.12, 2.1, 2.06, 2, 1.91, 1.82, 1.71, 1.61, 1.5, 1.4,
            1.32, 1.25, 1.2, 1.16, 1.13, 1.1, 1.06, 1.03, 1, 0.97, 0.93, 0.9,
            0.87, 0.85, 0.82, 0.79, 0.77, 0.74, 0.72, 0.69, 0.67, 0.65, 0.63,
            0.61, 0.59, 0.58, 0.56, 0.54, 0.53, 0.52, 0.51, 0.5, 0.49, 0.48,
            0.48, 0.47, 0.47, 0.46, 0.46, 0.47, 0.48, 0.5, 0.53, 0.56, 0.59,
            0.62, 0.64, 0.67, 0.69, 0.7, 0.71, 0.71, 0.71, 0.71, 0.7, 0.7, 0.7,
            0.69, 0.69, 0.69, 0.68, 0.68, 0.67, 0.67, 0.67, 0.66, 0.66, 0.65,
            0.65, 0.65, 0.65, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64, 0.64,
            0.64, 0.64, 0.64, 0.65, 0.65, 0.65, 0.66, 0.66, 0.67, 0.68, 0.69,
            0.69, 0.7, 0.71, 0.73, 0.74, 0.75, 0.76, 0.78, 0.8, 0.81, 0.83,
            0.85, 0.87, 0.89, 0.92, 0.94, 0.97, 0.99, 1.02, 1.05, 1.08, 1.11,
            1.15, 1.18, 1.32, 1.66, 2.21, 2.97, 3.94, 5.11, 6.5, 8.1, 9.9,
            11.92, 14.15, 16.6, 22.3, 22.8, 24.48, 30.38, 35.74, 42.4, 57.14,
            94.04, 112.9, 123.4, 130.4, 130, 119.4, 120.7, 116.8, 118.1, 119.4,
            124.8, 143.5, 204, 294, 319.2, 328.4, 365, 350.8, 347.6, 347.6, 325,
            331.6, 319.2, 308, 308, 308, 308, 296.8, 300, 281, 278.4, 270.6,
            271, 253.6, 233.5, 219.2, 207.8, 205.9, 204, 189.6, 178.8, 173.4,
            160, 154.4, 146, 145, 140.5, 130.4, 126.2, 116.8, 112.9, 106.5,
            101.6, 98.51, 82.67, 67.3, 80.05, 76.12, 72.3, 71.02, 69.78, 67.3,
            67.3, 68.54, 57.6, 71.02, 66.06, 59.12, 57.14, 55.16, 55.16, 52.19,
            52.19, 51.2, 48.56, 44.16, 43, 45.92, 49.44, 44.16, 36.48, 35.74,
            35, 32.36, 37.22, 32.36, 32.36, 32.36, 33.68, 32.36, 31.7, 35.74,
            29.72, 32.36, 30.38, 29.72, 28.4, 28.4, 28.4, 27.28, 25.6, 25.04,
            23.92, 22.3, 21.8, 21.8, 21.8, 22.8, 21.8, 25.6, 22.8, 22.8, 17.8,
            16.04, 16.04, 16.04, 16.04, 16.04, 16.04, 16.04, 16.04, 16.04,
            16.04, 15.02, 14, 14.03, 14.11, 14.25, 14.45, 14.72, 15.06, 15.46,
            15.95, 16.51, 17.15, 17.87, 18.69, 19.59, 20.59, 21.69, 22.88,
            24.18, 25.59, 27.1, 28.73, 30.48, 32.34, 34.33, 36.44, 38.69, 41.06,
            43.57, 46.22, 49.01, 51.95, 55.04, 58.27, 61.66, 65.21, 68.92, 72.8,
            88.09, 104.9, 105.7, 110.3, 111.6, 110.3, 106.5, 105.7, 103.3, 100,
            97.02, 98.8, 91.07, 83.98, 88.09, 81.36, 78.74, 77.43, 77.43, 73.5,
            74.81, 72.63, 68.58, 66.4, 68.54, 69.78, 67.3, 64.82, 61.1, 59.12,
            56.15, 53.18, 50.32, 49.44, 44.16, 36.5, 42.4, 37.96, 37.22, 33.68,
            36.48, 35.74, 35, 35, 37.22, 37.22, 39.44, 32.6, 34.54, 36.48,
            35.74, 34.34, 33.68, 33.02, 31.04, 29.72, 29.72, 29.72, 26.16, 25.6,
            29.72, 18.3, 22.3, 21.3, 21.8, 21.8, 20.3, 20.8, 25.04, 25.04, 25.6,
            25.6, 25.04, 25.6, 25.04, 25.6, 23.92, 25.04, 21.3, 21.8, 22.3,
            21.8, 20.8, 16.1, 20.3, 18.3, 13.22, 19.3, 19.3, 18.3, 14.4, 13.86,
            13.36, 12.9, 12.48, 12.1, 11.75, 11.43, 11.15, 10.9, 10.67, 10.48,
            10.31, 10.16, 10.04, 9.93, 9.85, 9.78, 9.73, 9.69, 9.67, 9.65, 9.65,
            12.08, 8.67, 11.7, 11.38, 10.65, 9.84, 9.32, 9.07, 8.85, 8.66, 8.49,
            8.35, 8.22, 8.1, 7.98, 7.86, 7.74, 7.61, 7.47, 7.31, 7.14, 6.96,
            6.78, 6.58, 6.39, 6.19, 5.99, 5.78, 5.58, 5.39, 5.2, 5.01, 4.83,
            4.67, 4.51, 4.37, 4.24, 4.12, 4.02, 3.95, 3.89, 3.85, 3.84, 4.41,
            5.77, 7.39, 8.75, 9.32, 9.18, 9, 8.94, 8.88, 8.83, 8.78, 8.73, 8.68,
            8.64, 8.6, 8.56, 8.53, 8.5, 8.47, 8.45, 8.42, 8.4, 8.39, 8.37, 8.36,
            8.35, 8.35, 8.34, 8.34, 8.67, 9.65, 9.62, 9.53, 9.4, 9.21, 8.98,
            8.7, 8.4, 8.06, 7.69, 7.3, 6.89, 6.47, 6.03, 5.59, 5.14, 4.7, 4.26,
            3.83, 3.42, 3.02, 2.65, 2.3, 1.98, 1.7, 1.45, 1.25, 1.09, 0.99,
            0.94, 0.92, 0.91, 0.89, 0.87, 0.85, 0.84, 0.82, 0.81, 0.79, 0.78,
            0.77, 0.75, 0.74, 0.73, 0.72, 0.71, 0.7, 0.69, 0.68, 0.67, 0.66,
            0.65, 0.64, 0.64, 0.63, 0.63, 0.62, 0.62, 0.61, 0.61, 0.61, 0.6,
            0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6,
            0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6,
            0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6,
            0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.6, 0.61, 0.61, 0.61, 0.61, 0.61,
            0.61, 0.62, 0.62, 0.62, 0.62, 0.63, 0.63, 0.63, 0.63, 0.63, 0.64,
            0.64, 0.64, 0.64, 0.64, 0.65, 0.65, 0.65, 0.65, 0.65, 0.65, 0.65,
            0.65, 0.65, 0.65, 0.65, 0.65, 0.65, 0.65, 0.65, 0.65, 0.65, 0.65,
            0.65, 0.65, 0.65, 0.65, 0.65, 0.64, 0.63, 0.62, 0.6, 0.59, 0.57,
            0.55, 0.54, 0.53, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.51, 0.51, 0.51, 0.5, 0.5, 0.49, 0.48, 0.47, 0.47, 0.46, 0.45,
            0.45, 0.44, 0.43, 0.42, 0.42, 0.41, 0.41, 0.41, 0.4, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.41, 0.42, 0.43, 0.44,
            0.46, 0.48, 0.5, 0.53, 0.55, 0.58, 0.61, 0.64, 0.67, 0.7, 0.73,
            0.77, 0.8, 0.83, 0.87, 0.9, 0.93, 0.96, 0.99, 1.02, 1.05, 1.08, 1.1,
            1.12, 1.14, 1.16, 1.17, 1.18, 1.19, 1.2, 1.2, 1.2, 1.19, 1.17, 1.15,
            1.12, 1.09, 1.06, 1.02, 0.98, 0.94, 0.9, 0.86, 0.82, 0.78, 0.74,
            0.7, 0.66, 0.63, 0.6, 0.57, 0.55, 0.53, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52, 0.52,
            0.52, 0.52, 0.52, 0.51, 0.51, 0.5, 0.5, 0.49, 0.49, 0.48, 0.47,
            0.47, 0.47, 0.46, 0.46, 0.45, 0.45, 0.45, 0.44, 0.44, 0.44, 0.43,
            0.43, 0.43, 0.42, 0.42, 0.42, 0.41, 0.41, 0.41, 0.41, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,
            0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.41, 0.41, 0.41,
            0.41, 0.41, 0.41, 0.41, 0.41, 0.41, 0.41, 0.41, 0.41, 0.41, 0.41,
            0.41, 0.42, 0.42, 0.42, 0.42, 0.42, 0.42, 0.42, 0.42, 0.42, 0.43,
            0.43, 0.43, 0.43, 0.43, 0.43, 0.44, 0.44, 0.44, 0.44, 0.44, 0.44,
            0.45, 0.45, 0.45,
          ],
        },
        {
          name: "Rainfall",
          type: "line",
          yAxisIndex: 1,
          sampling: sampling,
          notShowSymbol: true,
          hoverAnimation: false,
          itemStyle: { normal: { areaStyle: { type: "default" } } },
          markArea: {
            silent: true,
            data: [
              [
                {
                  // name: 'a',
                  xAxis: "2009/9/26 7:00",
                  // xAxis: 1
                },
                {
                  // name: 'b',
                  xAxis: "2009/10/8 7:00",
                  // xAxis: 5
                },
              ],
            ],
          },
          data: [
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.005, 0.017, 0.017, 0.017, 0.017,
            0.011, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0.021, 0.026, 0.03, 0.036, 0.036, 0.195, 0.221, 0.019, 0.013,
            0.017, 0.03, 0.03, 0.03, 0.046, 0.045, 0.038, 0.084, 0.045, 0.045,
            0.037, 0.034, 0.035, 0.036, 0.044, 0.052, 0.048, 0.109, 0.033,
            0.029, 0.04, 0.042, 0.042, 0.042, 0.073, 0.076, 0.062, 0.066, 0.066,
            0.075, 0.096, 0.128, 0.121, 0.128, 0.14, 0.226, 0.143, 0.097, 0.018,
            0, 0, 0, 0, 0, 0.018, 0.047, 0.054, 0.054, 0.054, 0.036, 0.185,
            0.009, 0.038, 0.061, 0.077, 0.091, 0.126, 0.69, 0.182, 0.349, 0.231,
            0.146, 0.128, 0.167, 0.1, 0.075, 0.071, 0.071, 0.117, 0.01, 0.002,
            0.002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0.005, 0.026, 0.038, 0.038, 0.038, 0.076,
            0.086, 0.109, 0.213, 0.276, 0.288, 0.297, 0.642, 1.799, 1.236,
            2.138, 0.921, 0.497, 0.685, 0.828, 0.41, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0.018, 0.024, 0.024, 0.024, 0.024, 0.006, 0.003,
            0.046, 0.046, 0.046, 0.046, 0.043, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0.204, 0.303, 1.028, 1.328, 1.524, 1.41, 1.362,
            1.292, 1.191, 0.529, 0.501, 0.944, 1.81, 2.899, 0.859, 0.126, 0.087,
            0.047, 0, 0, 0, 0, 0.011, 0.028, 0.028, 0.028, 0.028, 0.017, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.099, 0.159, 0.297,
            0.309, 0.309, 0.614, 0.818, 1.436, 1.195, 0.553, 0.542, 0.955,
            0.898, 0.466, 0.386, 0.556, 0.388, 0.221, 0.192, 0.192, 0.187,
            0.166, 0.18, 0.302, 0.158, 0.009, 0.009, 0.009, 0.009, 0.009, 0.007,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0.004, 0.032, 0.032, 0.032, 0.032, 0.082,
            0.149, 0.204, 0.247, 0.262, 0.49, 0.51, 0.533, 0.746, 0.847, 2.393,
            1.188, 1.114, 0.475, 0.043, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0.017, 0.017, 0.021, 0.042, 0.079, 0.111, 0.126, 0.122, 0.133,
            0.846, 0.102, 0.077, 0.067, 0.056, 0.005, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0.011, 0.017, 0.017, 0.017, 0.017, 0.006, 0, 0, 0, 0, 0,
            0.01, 0.03, 0.054, 0.067, 0.07, 0.25, 0.251, 0.494, 0.065, 0.054,
            0.054, 0.064, 0.084, 0.077, 0.101, 0.132, 0.248, 0.069, 0.117,
            0.115, 0.087, 0.326, 0.036, 0.009, 0.009, 0.009, 0.009, 0.009,
            0.004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0.02, 0.039, 0.04, 0.04, 0.04, 0.229, 0.079, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0.023, 0.069, 0.082, 0.082, 0.082, 0.503, 0.774, 0.038,
            0.012, 0.012, 0.012, 0.016, 0.02, 0.028, 0.051, 0.06, 0.064, 0.19,
            0.15, 0.164, 0.139, 0.13, 0.085, 0.031, 0.023, 0.022, 0.007, 0.005,
            0.005, 0.001, 0, 0.02, 0.048, 0.048, 0.053, 0.056, 0.036, 0.008,
            0.008, 0.004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.013, 0.017, 0.036, 0.068, 0.095,
            0.233, 0.272, 0.377, 0.722, 1.494, 3.756, 0.954, 0.439, 0.442,
            0.462, 0.373, 0.249, 0.214, 0.1, 0.044, 0.037, 0.023, 0.002, 0, 0,
            0, 0, 0, 0, 0.02, 0.024, 0.024, 0.024, 0.024, 0.004, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.008, 0.017, 0.017,
            0.045, 0.186, 0.308, 0.241, 0.241, 0.893, 4.067, 4.494, 5.015,
            3.494, 2.057, 1.411, 0.718, 0.407, 0.313, 0.339, 1.537, 1.105,
            0.218, 0.136, 0.03, 0.005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0.037, 0.448, 1.2, 1.309, 1.309, 1.425, 1.223, 0.471,
            0.767, 0.423, 0.273, 0.412, 0.646, 0.481, 0.239, 0.131, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0.044, 0.15, 0.223, 0.388, 0.513, 0.883, 2.828, 4.786, 5.959, 4.95,
            6.434, 6.319, 3.35, 2.806, 4.204, 1.395, 1.015, 1.015, 0.836, 0.74,
            0.72, 0.615, 0.477, 0.192, 0.046, 0.007, 0.007, 0.007, 0.007, 0.007,
            0.007, 0.007, 0.008, 0.005, 0.005, 0.005, 0.005, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0.001, 0.012, 0.012, 0.012, 0.012, 0.011,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0.002, 0.012, 0.028, 0.028, 0.028, 0.138, 0.092, 0.082, 0.082,
            0.096, 0.719, 0.155, 0.042, 0.047, 0.129, 0.021, 0.021, 0.014,
            0.009, 0.029, 0.067, 0.088, 0.095, 0.095, 0.138, 0.091, 0.032,
            0.025, 0.025, 0.003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.002,
            0.045, 0.228, 0.297, 0.325, 0.339, 0.581, 1.244, 0.796, 0.517,
            0.227, 0.053, 0.006, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.003, 0.005, 0.005,
            0.005, 0.005, 0.081, 0.129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0.014, 0.041, 0.041, 0.041, 0.041, 0.027, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0.009, 0.017, 0.017, 0.017, 0.017, 0.355,
            0.174, 0.009, 0.009, 0.012, 0.136, 0.208, 0.208, 0.208, 0.215,
            7.359, 1.858, 0.458, 0.053, 0.053, 0.047, 0.045, 0.045, 0.059,
            0.136, 0.188, 0.206, 0.21, 0.588, 1.517, 6.02, 4.688, 4.42, 0.624,
            0.326, 0.359, 0.553, 0.899, 0.94, 2.95, 9.415, 5.752, 1.092, 0.096,
            0.035, 0.026, 0.018, 0.015, 0.011, 0.011, 0.011, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0.056, 0.27, 0.314, 0.351, 0.354, 0.609, 0.796,
            1.857, 0.848, 0.538, 0.214, 0.178, 0.178, 0.201, 0.231, 0.227,
            0.272, 0.397, 0.45, 1.014, 2.917, 1.675, 0.081, 0.059, 0.059, 0.148,
            0.075, 0.075, 0.078, 0.236, 0.784, 0.784, 0.784, 0.784, 0.741,
            0.115, 0.058, 0.058, 0.058, 0.029, 0.015, 0.015, 0.015, 0.015,
            0.012, 0.008, 0.604, 0.985, 1.305, 2.273, 2.528, 2.336, 2.496,
            2.281, 1.397, 1.713, 3.259, 1.167, 0.745, 0.548, 1.058, 0.684,
            0.728, 0.392, 0.179, 0.283, 0.283, 0.46, 0.08, 0.099, 0.099, 0.099,
            0.1, 0.143, 0.137, 0.238, 0.317, 0.262, 0.225, 0.792, 0.426, 0.332,
            0.261, 0.11, 0.093, 0.102, 0.171, 0.292, 0.504, 0.605, 1.745, 2.485,
            1.964, 0.33, 0.171, 0.259, 0.242, 0.215, 0.366, 0.354, 0.205, 0.203,
            0.262, 0.153, 0.13, 0.137, 0.362, 0.691, 0.295, 0.433, 0.154, 0.056,
            0.053, 0.053, 0.053, 0.051, 0.047, 0.065, 0.078, 0.091, 0.206,
            0.813, 0.102, 0.151, 0.05, 0.024, 0.004, 0.001, 0, 0, 0, 0.021,
            0.021, 0.021, 0.021, 0.021, 0.013, 0.013, 0.013, 0.013, 0.013,
            0.013, 0.013, 0.013, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,
            0.01, 0.01, 0.008, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0.018, 0.021, 0.021, 0.021, 0.021, 0.003, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0.024, 0.173, 0.261, 0.267, 0.267, 0.534, 1.354, 1.772, 0.72,
            0.218, 0.018, 0.018, 0.028, 0.036, 0.032, 0.194, 0.082, 0.035,
            0.286, 0.027, 0.038, 0.038, 0.027, 0.021, 0.014, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0.016, 0.017, 0.017, 0.031, 0.047, 0.043, 0.056, 0.104,
            0.149, 0.179, 0.205, 0.328, 0.998, 0.522, 1.851, 3.727, 3.273,
            2.204, 1.169, 1.006, 1.179, 0.74, 0.741, 1.065, 0.925, 0.671, 0.497,
            0.431, 0.327, 0.277, 0.126, 0.581, 0.207, 0.359, 2.485, 0.038,
            0.036, 0.003, 0.003, 0.003, 0.003, 0.004, 0.098, 0.023, 0.021,
            0.021, 0.022, 0.041, 0.041, 0.043, 0.045, 0.043, 0.014, 0.014,
            0.014, 0.014, 0.014, 0.014, 0.014, 0.031, 0.046, 0.063, 0.119,
            0.107, 0.092, 0.085, 0.065, 0.06, 0.054, 0.042, 0.039, 0.046, 0.044,
            0.028, 0.028, 0.02, 0.013, 0.013, 0.013, 0.013, 0.016, 0.032, 0.031,
            0.031, 0.031, 0.028, 0.011, 0.011, 0.011, 0.011, 0.011, 0.023,
            0.024, 0.024, 0.024, 0.019, 0.015, 0.015, 0.015, 0.015, 0.015,
            0.015, 0.013, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,
            0.01, 0.001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.011, 0.017, 0.024, 0.026, 0.061,
            0.172, 0.206, 0.213, 0.267, 0.511, 0.668, 0.157, 0.017, 0.017,
            0.017, 0.046, 0.054, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0.001, 0.017, 0.017, 0.017, 0.017, 0.016, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0.01, 0.017, 0.017, 0.017, 0.017, 0.012, 0.017, 0.017, 0.017, 0.017,
            0.012, 0, 0, 0, 0, 0, 0.003, 0.031, 0.066, 0.093, 0.112, 0.122,
            0.202, 0.068, 0.041, 0.022, 0.011, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0.002, 0.005, 0.012, 0.021, 0.021, 0.019, 0.033, 0.03, 0.026, 0.026,
            0.034, 0.095, 0.024, 0.024, 0.024, 0.023, 0.019, 0.018, 0.018,
            0.018, 0.011, 0.03, 0.045, 0.044, 0.044, 0.044, 0.022, 0.009, 0.024,
            0.033, 0.033, 0.033, 0.024, 0.009, 0, 0, 0, 0, 0, 0, 0.003, 0.017,
            0.017, 0.017, 0.017, 0.014, 0, 0, 0, 0, 0, 0.032, 0.032, 0.032,
            0.032, 0.032, 0.005, 0.008, 0.009, 0.014, 0.014, 0.009, 0.005,
            0.004, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.007,
            0.009, 0.009, 0.009, 0.009, 0.043, 0.063, 0.084, 0.098, 0.101,
            0.213, 0.334, 0.383, 0.43, 0.448, 0.511, 0.801, 0.835, 1.642, 1.614,
            1.496, 1.496, 1.476, 1.068, 0.481, 0.22, 0.119, 0.099, 0.07, 0.072,
            0.063, 0.076, 0.14, 0.205, 0.28, 0.297, 0.3, 0.479, 0.877, 1.098,
            1.611, 1.629, 1.686, 1.686, 1.631, 1.528, 1.862, 1.703, 1.531,
            2.196, 0.395, 0.416, 0.453, 0.728, 0.917, 0.986, 1.17, 2.171, 3.011,
            2.909, 3.301, 1.377, 0.778, 0.799, 0.947, 1.039, 0.879, 0.76, 1.372,
            1.674, 1.674, 1.68, 1.823, 1.793, 1.162, 0.783, 0.216, 0.152, 0.152,
            0.152, 0.049, 0, 0, 0, 0.117, 0.127, 0.127, 0.127, 0.127, 0.127, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.003,
            0.005, 0.005, 0.005, 0.005, 0.003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0.309, 0.364, 0.364, 0.364, 0.364, 0.063, 0.01,
            0.01, 0.01, 0.012, 0.015, 0.015, 0.11, 0.55, 0.824, 0.825, 0.829,
            1.39, 1.429, 1.342, 1.43, 1.636, 1.717, 2.135, 2.203, 3.191, 3.022,
            1.589, 0.86, 0.807, 0.645, 0.595, 0.588, 0.557, 0.552, 1.271, 0.708,
            0.677, 0.629, 0.714, 0.203, 0.133, 0.061, 0.062, 0.018, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0.001, 0.072, 0.29, 0.438, 0.53, 0.557, 0.873, 1.039, 1.04,
            0.208, 0.049, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0.03, 0.039, 0.039, 0.039, 0.039, 0.098, 0.008, 0.007,
            0.007, 0.007, 0.007, 0.007, 0.007, 0.007, 0.007, 0.007, 0.056,
            0.062, 0.065, 0.065, 0.065, 0.047, 0.216, 0.256, 0.315, 0.4, 0.502,
            0.449, 0.47, 0.571, 0.814, 1.153, 0.774, 0.202, 0.086, 0.075, 0.071,
            0.032, 0.019, 0.003, 0.004, 0.004, 0.004, 0.004, 0.004, 0.004,
            0.007, 0.072, 0.153, 0.256, 0.306, 0.404, 0.698, 0.733, 0.823,
            0.715, 0.563, 0.404, 0.293, 0.217, 0.213, 0.202, 0.202, 0.294,
            0.704, 0.797, 1.359, 1.101, 0.72, 0.514, 0.539, 0.434, 0.389, 0.387,
            0.386, 0.375, 0.369, 0.319, 0.239, 0.183, 0.136, 0.062, 0.052,
            0.096, 0.119, 0.119, 0.114, 0.127, 0.132, 0.139, 0.169, 0.191,
            0.278, 0.254, 0.214, 0.237, 0.221, 0.143, 0.129, 0.125, 0.109, 0.1,
            0.087, 0.06, 0.038, 0.029, 0.029, 0.028, 0.048, 0.053, 0.053, 0.111,
            0.125, 0.102, 0.097, 0.097, 0.039, 0.02, 0.02, 0.02, 0.014, 0.004,
            0.031, 0.043, 0.047, 0.052, 0.08, 0.144, 0.182, 0.176, 0.171, 0.149,
            0.112, 0.025, 0, 0, 0, 0, 0, 0, 0, 0.016, 0.031, 0.031, 0.031,
            0.031, 0.015, 0, 0, 0, 0, 0, 0.005, 0.005, 0.005, 0.005, 0.005, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0.005, 0.005, 0.005, 0.005, 0.005, 0.001, 0, 0, 0,
          ],
        },
      ],
    });

    window.onresize = chart.resize;
  });

  // pie chart js
  require((testHelper.hasURLParam("en")
    ? [
        "echarts",
        // 'echarts/lang/en',
      ]
    : ["echarts"]
  ).concat([
    // 'echarts/chart/bar',
    // 'echarts/chart/line',
    // 'echarts/component/legend',
    // 'echarts/component/graphic',
    // 'echarts/component/grid',
    // 'echarts/component/tooltip',
    // 'echarts/component/brush',
    // 'echarts/component/toolbox',
    // 'echarts/component/title',
    // 'zrender/vml/vml'
  ]), function (echarts) {
    var chart = echarts.init(document.getElementById("echart-pie"));

    chart.setOption({
      aria: {
        enabled: true,
      },
      title: {
        text: "Source of user visits to a site",
        subtext: "Purely fictitious",
        x: "center",
      },
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b} : {c} ({d}%)",
      },
      legend: {
        orient: "vertical",
        left: "left",
        data: [
          "direct interview",
          "Email marketing",
          "Affiliate advertising",
          "Video ad(value is null)",
          "search engine",
        ],
      },
      series: [
        {
          name: "Visit source",
          type: "pie",
          radius: "55%",
          center: ["50%", "60%"],
          selectedMode: "single",
          data: [
            { value: 335, name: "direct interview" },
            { value: 310, name: "Email marketing" },
            { value: 234, name: "Affiliate advertising" },
            { value: null, name: "Video ad(value is null)" },
            { value: 1548, name: "search engine" },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    });

    chart.on("pieselectchanged", function (e) {
      console.log(e);
    });

    window.onresize = chart.resize;
  });
  // bar polar chart js
  require([
    "echarts",
    // 'echarts/chart/bar',
    // 'echarts/component/legend',
    // 'echarts/component/polar',
    // 'echarts/component/title',
    // 'echarts/component/tooltip',
    // 'zrender/vml/vml'
  ], function (echarts) {
    var chart = echarts.init(document.getElementById("echart-polar"));

    var data = [
      [5000, 10000, 6785.71],
      [4000, 10000, 6825],
      [3000, 6500, 4463.33],
      [2500, 5600, 3793.83],
      [2000, 4000, 3060],
      [2000, 4000, 3222.33],
      [2500, 4000, 3133.33],
      [1800, 4000, 3100],
      [2000, 3500, 2750],
      [2000, 3000, 2500],
      [1800, 3000, 2433.33],
      [2000, 2700, 2375],
      [1500, 2800, 2150],
      [1500, 2300, 2100],
      [1600, 3500, 2057.14],
      [1500, 2600, 2037.5],
      [1500, 2417.54, 1905.85],
      [1500, 2000, 1775],
      [1500, 1800, 1650],
    ];
    var cities = [
      "Beijing",
      "Shanghai",
      "Shenzhen",
      "Guangzhou",
      "State Su",
      "Hangzhou",
      "Nanjing",
      "Fuzhou",
      "Qingdao",
      "Jinan",
      "Changchun",
      "Dalian",
      "Wenzhou",
      "Zhengzhou",
      "Wuhan",
      "Chengdu",
      "Dongguan",
      "Shenyang",
      "Yantai",
    ];
    var barHeight = 50;

    chart.setOption({
      title: {
        text: "How expensive is it to rent a house in China?",
        subtext:
          "Monthly rent for a room in the city center（Data Sources：https://www.numbeo.com）",
      },
      legend: {
        show: true,
        data: ["price range", "Mean"],
      },
      grid: {
        top: 100,
      },
      angleAxis: {
        type: "category",
        data: cities,
      },
      tooltip: {
        show: true,
        formatter: function (params) {
          var id = params.dataIndex;
          return (
            cities[id] +
            "<br>highest：" +
            data[id][0] +
            "<br>lowest：" +
            data[id][1] +
            "<br>average：" +
            data[id][2]
          );
        },
      },
      radiusAxis: {},
      polar: {},
      series: [
        {
          type: "bar",
          itemStyle: {
            normal: {
              color: "transparent",
            },
          },
          data: data.map(function (d) {
            return d[0];
          }),
          coordinateSystem: "polar",
          stack: "Maximum and minimum",
          silent: true,
        },
        {
          type: "bar",
          data: data.map(function (d) {
            return d[1] - d[0];
          }),
          coordinateSystem: "polar",
          name: "price range",
          stack: "Maximum and minimum",
        },
        {
          type: "bar",
          itemStyle: {
            normal: {
              color: "transparent",
            },
          },
          data: data.map(function (d) {
            return d[2] - barHeight;
          }),
          coordinateSystem: "polar",
          stack: "Mean",
          silent: true,
          z: 10,
        },
        {
          type: "bar",
          data: data.map(function (d) {
            return barHeight * 2;
          }),
          coordinateSystem: "polar",
          name: "Mean",
          stack: "Mean",
          barGap: "-100%",
          z: 10,
        },
      ],
      legend: {
        show: true,
        data: ["A", "B", "C"],
      },
    });

    chart.on("click", function (params) {
      console.log(params);
    });

    window.onresize = chart.resize;
  });

  // bar chart js
  require((testHelper.hasURLParam("en")
    ? [
        "echarts",
        // 'echarts/lang/en',
      ]
    : ["echarts"]
  ).concat([
    // 'echarts/chart/bar',
    // 'echarts/chart/line',
    // 'echarts/component/legend',
    // 'echarts/component/graphic',
    // 'echarts/component/grid',
    // 'echarts/component/tooltip',
    // 'echarts/component/brush',
    // 'echarts/component/toolbox',
    // 'echarts/component/title',
    // 'zrender/vml/vml'
  ]), function (echarts) {
    var chart = echarts.init(document.getElementById("echart-bar"));

    var xAxisData = [];
    var data1 = [];
    var data2 = [];
    var data3 = [];
    var data4 = [];

    for (var i = 0; i < 10; i++) {
      xAxisData.push("Category" + i);
      data1.push(i === 0 ? "-" : (Math.random() * 5).toFixed(2));
      data2.push(-Math.random().toFixed(2));
      data3.push((Math.random() + 0.5).toFixed(2));
      data4.push((Math.random() + 0.3).toFixed(2));
    }

    var itemStyle = {
      normal: {
        barBorderRadius: 5,
        label: {
          show: true,
          position: "outside",
        },
      },
      emphasis: {
        focus: "series",
        label: {
          position: "outside",
        },
        // barBorderColor: '#fff',
        // barBorderWidth: 1,
        // shadowBlur: 10,
        // shadowOffsetX: 0,
        // shadowOffsetY: 0,
        shadowColor: "rgba(0,0,0,0.3)",
      },
    };

    chart.setOption({
      legend: {
        left: 150,
        inactiveColor: "#abc",
        borderWidth: 1,
        data: [
          {
            name: "bar",
          },
          "bar2",
          "\n",
          "bar3",
          "bar4",
        ],
        selected: {
          // 'bar': false
        },
        // orient: 'vertical',
        // x: 'right',
        // y: 'bottom',
        align: "left",

        tooltip: {
          show: true,
        },
      },
      brush: {
        xAxisIndex: 0,
      },
      toolbox: {
        top: 50,
        // right: 20,
        feature: {
          magicType: {
            type: ["line", "bar", "stack", "tiled"],
          },
          dataView: {},
          saveAsImage: {
            pixelRatio: 2,
          },
          brush: {
            type: ["rect", "polygon", "lineX", "lineY", "keep", "clear"],
          },
          restore: {},
          dataZoom: {},
          myTool1: {
            show: true,
            title: "Custom extension method 1",
            icon: "path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891",
            onclick: function () {
              alert("myToolHandler1");
            },
          },
          myTool2: {
            show: true,
            title: "Custom extension method 2",
            // icon: 'image://./assets/echarts-logo.png',
            onclick: function () {
              alert("myToolHandler2");
            },
          },
        },

        iconStyle: {
          emphasis: {
            textPosition: "top",
            // textAlign: 'right'
          },
        },
      },
      tooltip: {},
      grid: {
        top: 100,
      },
      xAxis: {
        data: xAxisData,
        name: "Horizontal axis",
        silent: false,
        axisTick: {
          alignWithLabel: true,
        },
        // axisLabel: {
        //     show: false
        // },
        // axisTick: {
        //     show: false
        // },
        axisLine: {
          onZero: true,
          // lineStyle: {
          //     width: 5
          // }
        },
        splitLine: {
          show: true,
        },
        splitArea: {
          show: true,
        },
      },
      yAxis: {
        inverse: true,
        // axisLabel: {
        //     show: false
        // },
        // axisLine: {
        //     lineStyle: {
        //         width: 5
        //     }
        // },
        axisTick: {
          show: false,
        },
        // splitLine: {
        //     show: false
        // },
        splitArea: {
          show: false,
        },
      },
      series: [
        {
          name: "bar",
          type: "bar",
          stack: "one",
          itemStyle: itemStyle,
          selectedMode: true,
          cursor: "move",
          data: data1,
        },
        {
          name: "bar2",
          type: "bar",
          stack: "one",
          itemStyle: itemStyle,
          selectedMode: true,
          cursor: "default",
          data: data2,
        },
        {
          name: "bar3",
          type: "bar",
          stack: "two",
          itemStyle: itemStyle,
          selectedMode: true,
          data: data3,
        },
        {
          name: "bar4",
          type: "bar",
          stack: "two",
          itemStyle: itemStyle,
          selectedMode: true,
          data: data4,
        },
      ],
    });

    chart.on("click", function (params) {
      console.log(params);
    });

    chart.on("legendselectchanged", function (params) {
      chart.setOption({
        // title: {
        // },
        graphic: [
          {
            type: "circle",
            shape: {
              cx: 100,
              cy: 100,
              r: 20,
            },
          },
        ],
      });
    });

    window.onresize = chart.resize;
  });

  // bar overflow time plot js
  require([
    "echarts",
    // 'echarts/chart/bar',
    // 'echarts/component/legend',
    // 'echarts/component/grid',
    // 'echarts/component/tooltip',
    // 'echarts/component/markLine'
  ], function (echarts) {
    var chart = echarts.init(document.getElementById("echart-bar2"), null, {});
    chart.setOption({
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
        },
      },
      legend: {
        data: [
          "direct interview",
          "Email marketing",
          "Affiliate advertising",
          "Video ad",
          "search engine",
          "Baidu",
          "Google",
          "must",
          "other",
        ],
      },
      toolbox: {
        show: true,
        orient: "vertical",
        left: "right",
        top: "center",
        feature: {
          mark: { show: true },
          dataView: { show: true, readOnly: false },
          magicType: { show: true, type: ["line", "bar", "stack", "tiled"] },
          restore: { show: true },
          saveAsImage: { show: true },
        },
      },
      calculable: true,
      xAxis: [
        {
          type: "category",
          data: [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
          ],
        },
      ],
      yAxis: [
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "direct interview",
          type: "bar",
          data: [320, 332, 301, 334, 390, 330, 320],
        },
        {
          name: "Email marketing",
          type: "bar",
          stack: "advertising",
          data: [120, 132, 101, 134, 90, 230, 210],
        },
        {
          name: "Affiliate advertising",
          type: "bar",
          stack: "advertising",
          data: [220, 182, 191, 234, 290, 330, 310],
        },
        {
          name: "Video ad",
          type: "bar",
          stack: "advertising",
          data: [150, 232, 201, 154, 190, 330, 410],
        },
        {
          name: "search engine",
          type: "bar",
          data: [862, 1018, 964, 1026, 1679, 1600, 1570],
          markLine: {
            itemStyle: {
              normal: {
                label: {
                  formatter: function (params) {
                    console.log(params);
                  },
                },
                lineStyle: {
                  type: "dashed",
                },
              },
            },
            data: [[{ type: "min" }, { type: "max" }]],
          },
        },
        {
          name: "Baidu",
          type: "bar",
          barWidth: 5,
          stack: "search engine",
          data: [620, 732, 701, 734, 1090, 1130, 1120],
        },
        {
          name: "Google",
          type: "bar",
          stack: "search engine",
          data: [120, 132, 101, 134, 290, 230, 220],
        },
        {
          name: "must",
          type: "bar",
          stack: "search engine",
          data: [60, 72, 71, 74, 190, 130, 110],
        },
        {
          name: "other",
          type: "bar",
          stack: "search engine",
          data: [62, 82, 91, 84, 109, 110, 120],
        },
      ],
    });
    window.onresize = chart.resize;
  });

  // boxplot chart js
  /**
   * @see <https://en.wikipedia.org/wiki/Michelson%E2%80%93Morley_experiment>
   * @see <http://bl.ocks.org/mbostock/4061502>
   */
  var chart;
  var data;
  var mean;

  require([
    "echarts",
    "assets/js/chart/echart/data/Michelson-Morley.json",
  ], function (echarts, rawData) {
    var env = echarts.env;

    chart = echarts.init(document.getElementById("echart-boxplot"));

    update("horizontal");

    initControlPanel(env);

    function update(layout) {
      // mean = calculateMean(rawData);

      var categoryAxis = {
        type: "category",
        boundaryGap: true,
        nameGap: 30,
        splitArea: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      };
      var valueAxis = {
        type: "value",
        name: "km/s minus 299,000",
        splitArea: {
          show: true,
        },
      };

      chart.setOption({
        aria: {
          show: true,
        },
        dataset: [
          {
            source: rawData,
          },
          {
            transform: {
              type: "boxplot",
              config: { itemNameFormatter: "expr {value}" },
            },
          },
          {
            fromDatasetIndex: 1,
            fromTransformResult: 1,
          },
        ],
        title: [
          {
            text: "Michelson-Morley Experiment",
            left: "center",
          },
        ],
        legend: {
          left: "right",
        },
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "10%",
          right: "10%",
          bottom: "15%",
        },
        xAxis: layout === "horizontal" ? categoryAxis : valueAxis,
        yAxis: layout === "vertical" ? categoryAxis : valueAxis,
        series: [
          {
            name: "boxplot",
            type: "boxplot",
            datasetIndex: 1,

            markPoint: {
              data: [
                {
                  name: "A certain coordinate",
                  coord: [2, 300],
                },
                {
                  name: "A screen coordinate",
                  x: 100,
                  y: 200,
                  label: {
                    normal: {
                      show: false,
                      formatter: "asdf",
                    },
                    emphasis: {
                      show: true,
                      position: "top",
                      formatter: "zxcv",
                    },
                  },
                },
                {
                  name: "max value (default)",
                  type: "max",
                },
                {
                  name: "min value (default)",
                  type: "min",
                },
                {
                  name: "max value (dim:Q1)",
                  type: "max",
                  valueDim: "Q1",
                },
                {
                  name: "average value (dim:Q1)",
                  type: "average",
                  valueDim: "Q1",
                },
              ],
            },

            markLine: {
              data: [
                [
                  {
                    name: "Marking line between two coordinates",
                    coord: [1, 240],
                  },
                  { coord: [2, 260] },
                ],
                [
                  {
                    name: "Mark line between two screen coordinates",
                    x: 50,
                    y: 60,
                  },
                  { x: 70, y: 90 },
                ],
                [{ name: "max - min", type: "max" }, { type: "min" }],
                {
                  name: "min line",
                  type: "min",
                },
                {
                  name: "max line on dim:Q3",
                  type: "max",
                  valueDim: "Q3",
                },
              ],
            },
          },
          {
            name: "outlier",
            type: "scatter",
            datasetIndex: 2,
          },
        ],
      });
    }

    // function calculateMean(rawData) {
    //     var sum = 0;
    //     var count = 0;
    //     for (var i = 0, len = rawData.length; i < len; i++) {
    //         for (var j = 0, lenj = rawData[i].length; j < lenj; j++) {
    //             var value = rawData[i][j];
    //             count++;
    //             if (!isNaN(value) && value != null && value !== '') {
    //                 sum += value;
    //             }
    //         }
    //     }
    //     return sum / count;
    // };

    function initControlPanel(env) {
      if (!env.browser.ie || env.browser.ie.version > 8) {
        var scr = document.createElement("script");
        scr.src = "../assets/js/chart/echart/lib/dat.gui.min.js";
        scr.onload = function () {
          var gui = new dat.GUI();
          var config = {
            layout: "horizontal",
          };
          gui
            .add(config, "layout", ["horizontal", "vertical"])
            .onChange(update);
        };
        document.head.appendChild(scr);
      }
    }
    window.onresize = chart.resize;
  });

  // lines clip chart js
  function makeToggleChartButtons(toggleClip) {
    return [
      {
        text: "Set Clip",
        onclick: function () {
          toggleClip(true);
        },
      },
      {
        text: "Set Visible",
        onclick: function () {
          toggleClip(false);
        },
      },
    ];
  }
  require(["echarts"], function (echarts) {
    var lineData = [];

    function getColor() {
      //定义字符串变量colorValue存放可以构成十六进制颜色值的值
      var colorValue = "0,1,2,3,4,5,6,7,8,9,a,b,c,d,e,f";
      //以","为分隔符，将colorValue字符串分割为字符数组["0","1",...,"f"]
      var colorArray = colorValue.split(",");
      var color = "#"; //定义一个存放十六进制颜色值的字符串变量，先将#存放进去
      //使用for循环语句生成剩余的六位十六进制值
      for (var i = 0; i < 6; i++) {
        //colorArray[Math.floor(Math.random()*16)]随机取出
        // 由16个元素组成的colorArray的某一个值，然后将其加在color中，
        //字符串相加后，得出的仍是字符串
        color += colorArray[Math.floor(Math.random() * 16)];
      }
      return color;
    }

    for (var i = 0; i < 20; ++i) {
      var x = Math.floor(Math.random() * 600 + 50);
      var y = Math.floor(Math.random() * 600 + 50);
      var xSign = Math.floor(Math.random() * 2 + 1);
      var ySign = Math.floor(Math.random() * 2 + 1);
      //负数
      if (xSign === 1) {
        x *= -1;
      }
      if (ySign === 1) {
        y *= -1;
      }

      var obj = {
        coords: [
          [0, 0],
          [x, y],
        ],
        label: {
          show: false,
        },
        lineStyle: {
          normal: {
            color: getColor(),
            width: 1,
          },
        },
      };
      lineData.push(obj);
    }

    option = {
      animation: false,
      xAxis: {
        type: "value",
        min: -1000,
        max: 1000,
        splitLine: {
          lineStyle: {
            width: 1,
          },
        },
      },
      yAxis: {
        type: "value",
        min: -1000,
        max: 1000,
        splitLine: {
          lineStyle: {
            width: 1,
          },
        },
      },
      dataZoom: [
        {
          type: "inside",
          xAxisIndex: 0,
          filterMode: "filter",
          start: 30,
          end: 70,
        },
        {
          type: "inside",
          yAxisIndex: 0,
          filterMode: "weakFilter",
          start: 30,
          end: 70,
        },
      ],
      series: [
        {
          type: "lines",
          name: "Network topology diagram",
          coordinateSystem: "cartesian2d",
          lineStyle: {
            normal: {
              color: "#F00",
              width: 1,
            },
          },
          label: {
            fontSize: 15,
          },
          symbol: ["none", "arrow"],
          // 数据
          data: lineData,
        },
      ],
    };

    var chart = testHelper.create(echarts, "lines-clip", {
      title:
        "Lines Clip,(case from #10748). Should not overflow after zoomed in",
      option: option,
      height: 400,
      buttons: makeToggleChartButtons(function (clip) {
        chart.setOption({
          series: [
            {
              clip: clip,
              data: lineData,
            },
          ],
        });
      }),
    });
    window.onresize = chart.resize;
  });

  // angle chart js
  require(["echarts"], function (echarts) {
    var _animationDuration = 1000;
    var _animationDurationUpdate = 1000;
    var _animationEasingUpdate = "elasticOut";
    var _datasourceList = [
      [[1, 156]],
      [[1, 54]],
      [[1, 131]],
      [[1, 32]],
      [[1, 103]],
      [[1, 66]],
    ];
    var _valOnRadianMax = 200;
    var _outerRadius = 100;
    var _innerRadius = 85;
    var _pointerInnerRadius = 40;
    var _insidePanelRadius = 65;
    var _currentDataIndex = 0;

    function renderItem(params, api) {
      var children = [];
      var dataIdx = params.dataIndex;
      var valOnRadian = api.value(1);
      var coords = api.coord([api.value(0), valOnRadian]);
      var polarEndRadian = coords[3];
      var imageStyle = {
        image: window.BAR_ROUND_GRADIENT_TEXTURE,
        x: params.coordSys.cx - _outerRadius,
        y: params.coordSys.cy - _outerRadius,
        width: _outerRadius * 2,
        height: _outerRadius * 2,
      };

      return {
        type: "group",
        children: [
          {
            type: "image",
            style: imageStyle,
            clipPath: {
              type: "sector",
              shape: {
                cx: params.coordSys.cx,
                cy: params.coordSys.cy,
                r: _outerRadius,
                r0: _innerRadius,
                startAngle: 0,
                // polor: anticlockwise-positive radian
                // sector: clockwise-positive radian
                endAngle: -polarEndRadian,
                transition: "endAngle",
                enterFrom: { endAngle: 0 },
              },
            },
          },
          {
            type: "image",
            style: imageStyle,
            clipPath: {
              type: "polygon",
              shape: {
                points: makePionterPoints(params, polarEndRadian),
              },
              extra: {
                polarEndRadian: polarEndRadian,
                transition: "polarEndRadian",
                enterFrom: { polarEndRadian: 0 },
              },
              during: function (apiDuring) {
                apiDuring.setShape(
                  "points",
                  makePionterPoints(
                    params,
                    apiDuring.getExtra("polarEndRadian")
                  )
                );
              },
            },
          },
          {
            type: "circle",
            shape: {
              cx: params.coordSys.cx,
              cy: params.coordSys.cy,
              r: _insidePanelRadius,
            },
            style: {
              fill: "#fff",
              shadowBlur: 25,
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              shadowColor: "rgb(0,0,50)",
            },
          },
          {
            type: "text",
            extra: {
              valOnRadian: valOnRadian,
              transition: "valOnRadian",
              enterFrom: { valOnRadian: 0 },
            },
            style: {
              text: makeText(valOnRadian),
              fontSize: 40,
              x: params.coordSys.cx,
              y: params.coordSys.cy,
              fill: "rgb(0,50,190)",
              align: "center",
              verticalAlign: "middle",
              enterFrom: { opacity: 0 },
            },
            during: function (apiDuring) {
              apiDuring.setStyle(
                "text",
                makeText(apiDuring.getExtra("valOnRadian"))
              );
            },
          },
        ],
      };
    }

    function convertToPolarPoint(renderItemParams, radius, radian) {
      return [
        Math.cos(radian) * radius + renderItemParams.coordSys.cx,
        -Math.sin(radian) * radius + renderItemParams.coordSys.cy,
      ];
    }

    function makePionterPoints(renderItemParams, polarEndRadian) {
      return [
        convertToPolarPoint(renderItemParams, _outerRadius, polarEndRadian),
        convertToPolarPoint(
          renderItemParams,
          _outerRadius,
          polarEndRadian + Math.PI * 0.03
        ),
        convertToPolarPoint(
          renderItemParams,
          _pointerInnerRadius,
          polarEndRadian
        ),
      ];
    }

    function makeText(valOnRadian) {
      // Validate additive animation calc.
      if (valOnRadian < -10) {
        alert("illegal during val: " + valOnRadian);
      }
      return ((valOnRadian / _valOnRadianMax) * 100).toFixed(0) + "%";
    }

    var option = {
      animationEasing: _animationEasingUpdate,
      animationDuration: _animationDuration,
      animationDurationUpdate: _animationDurationUpdate,
      animationEasingUpdate: _animationEasingUpdate,
      dataset: {
        source: _datasourceList[_currentDataIndex],
      },
      tooltip: {},
      angleAxis: {
        type: "value",
        startAngle: 0,
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        splitLine: { show: false },
        min: 0,
        max: _valOnRadianMax,
      },
      radiusAxis: {
        type: "value",
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        splitLine: { show: false },
      },
      polar: {},
      series: [
        {
          type: "custom",
          coordinateSystem: "polar",
          renderItem: renderItem,
        },
      ],
    };

    var chart = testHelper.create(echarts, "texture-bar-by-clipPath", {
      title: [
        "Angle gradient | clipPath animation",
        "click **next** to check the transition animation in both bar and text.",
      ],
      option: option,
      height: 300,
      buttons: [
        {
          text: "next",
          onclick: function () {
            _currentDataIndex++;
            _currentDataIndex >= _datasourceList.length &&
              (_currentDataIndex = 0);
            chart.setOption({
              dataset: {
                source: _datasourceList[_currentDataIndex],
              },
            });
          },
        },
      ],
    });

    window.onresize = chart.resize;
  });

  // gauge chart js
  require(["echarts" /*, 'map/js/china' */], function (echarts) {
    var option4 = {
      tooltip: {
        formatter: "{a} <br/>{b} : {c}%",
      },
      toolbox: {
        feature: {
          restore: {},
          saveAsImage: {},
        },
      },
      series: [
        {
          // name: 'Business indicators',
          type: "gauge",
          pointer: {
            icon: "path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z",
          },
          progress: {
            show: true, // 坐标轴线
            overlap: true,
            roundCap: true,
          },
          axisLine: {
            roundCap: true,
          },
          data: [
            {
              value: 20,
              name: "Completion rate",
              title: {
                offsetCenter: ["-80%", "80%"],
              },
              detail: {
                offsetCenter: ["-80%", "95%"],
              },
            },
            {
              value: 40,
              name: "Compliance rate",
              title: {
                offsetCenter: ["0%", "80%"],
              },
              detail: {
                offsetCenter: ["0%", "95%"],
              },
            },
            {
              value: 60,
              name: "Excellent rate",
              title: {
                offsetCenter: ["80%", "80%"],
              },
              detail: {
                offsetCenter: ["80%", "95%"],
              },
            },
          ],
          title: {
            fontSize: 14,
          },
          detail: {
            width: 30,
            height: 12,
            fontSize: 12,
            color: "auto",
            // backgroundColor: 'auto',
            formatter: "{value}%",
          },
        },
      ],
    };
    var chart4 = testHelper.create(echarts, "echart-gauge", {
      option: option4,
    });
    window.addEventListener("resize", function () {
      chart4.resize();
    });
    setInterval(function () {
      option4.series[0].data[0].value = (Math.random() * 100).toFixed(2) - 0;
      option4.series[0].data[1].value = (Math.random() * 100).toFixed(2) - 0;
      option4.series[0].data[2].value = (Math.random() * 100).toFixed(2) - 0;
      chart4.setOption(option4, true);
    }, 2000);
  });

  // line visual chart js
  require(["echarts"], function (echarts) {
    var main = document.getElementById("line-visual");
    if (!main) {
      return;
    }
    var chart = echarts.init(main);

    var xAxisData = [];
    var data1 = [];

    var base = Math.round(Math.random() * 30);
    for (var i = 0; i < 1000; i++) {
      xAxisData.push("Category" + i);
      base += Math.round(Math.random() * 10 - 5);

      if (i > 500 && i < 550) {
        data1.push(180);
      } else if (i > 700 && i < 740) {
        data1.push(-90);
      } else {
        data1.push(base);
      }
    }

    chart.setOption({
      visualMap: {
        type: "piecewise",
        top: "center",
        inRange: {
          color: ["red", "green", "black"],
        },
        outOfRange: {
          color: "#aaa",
        },
        min: -70,
        max: 150,
      },
      dataZoom: {
        orient: "vertical",
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "line",
        },
      },
      xAxis: {
        data: xAxisData,
        boundaryGap: false,
      },
      yAxis: {},
      series: [
        {
          name: "line",
          type: "line",
          stack: "all",
          symbol: "circle",
          areaStyle: { normal: {} },
          symbolSize: 10,
          data: data1,
        },
      ],
    });

    window.onresize = chart.resize;
  });

  // line visual chart 2 js
  require(["echarts"], function (echarts) {
    var main = document.getElementById("line-visulH");
    if (!main) {
      return;
    }
    var chart = echarts.init(main);

    var data0 = [];

    var MAX_DIM1 = 100;

    var itemStyle = {
      normal: {
        opacity: 0.8,
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowOffsetY: 0,
        shadowColor: "rgba(0, 0, 0, 0.3)",
      },
    };

    var last = 60;
    var lastDelta = 20;
    for (var i = 0; i < MAX_DIM1; i++) {
      lastDelta += (Math.random() - 0.5) * 15;
      data0.push([i, (last += lastDelta)]);
    }

    chart.setOption({
      grid: {
        top: 100,
        bottom: 100,
      },
      xAxis: {
        type: "value",
        splitLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        splitLine: {
          show: false,
        },
      },
      visualMap: [
        {
          show: true,
          left: "center",
          bottom: 20,
          orient: "horizontal",
          itemWidth: 20,
          itemHeight: 200,
          min: 0,
          max: MAX_DIM1,
          calculable: true,
          range: [20, 95],
          dimension: 0,
          inRange: {
            colorHue: [0, 300],
            colorLightness: 0.35,
            colorSaturation: 1,
          },
          outOfRange: {
            color: "#eee",
          },
        },
      ],
      series: [
        {
          name: "hue",
          type: "line",
          barMaxWidth: 10,
          itemStyle: itemStyle,
          areaStyle: { normal: {} },
          data: data0,
        },
      ],
    });
  });

  // pictorial repeat chart js

  function makeChart(id, option, cb) {
    require([
      "echarts",
      // 'echarts/chart/pictorialBar',
      // 'echarts/chart/bar',
      // 'echarts/chart/scatter',
      // 'echarts/component/grid',
      // 'echarts/component/markLine',
      // 'echarts/component/legend',
      // 'echarts/component/tooltip',
      // 'echarts/component/dataZoom'
    ], function (echarts) {
      var main = document.getElementById(id);
      if (main) {
        var chartMain = document.createElement("div");
        chartMain.style.cssText = "height:100%";
        main.appendChild(chartMain);
        var chart = echarts.init(chartMain);
        chart.setOption(option);

        window.addEventListener("resize", chart.resize);

        cb && cb(echarts, chart);
      }
    });
  }

  var startData = 13000;
  var maxData = 18000;
  var minData = 5000;

  makeChart(
    "dynamic-data",
    {
      backgroundColor: "#f8f8f8",
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "none",
          label: { show: true },
        },
      },
      legend: {
        data: ["all"],
        textStyle: { color: "#2b2b2b" },
      },
      grid: {
        bottom: 100,
      },
      xAxis: [
        {
          data: [
            "standard",
            "fix symbol margin\n(not accurate)\n(but more comparable)",
            "use symbolBoundingData\nauto repeat times\n(accurate)\n(but symbolMargin not fixed)",
            "use symbolBoundingData\nfix repeat times\n(accurate)\n(but less responsive)",
          ],
          axisTick: { show: false },
          axisLine: {
            lineStyle: {
              color: "#ddd",
            },
          },
          axisLabel: {
            margin: 20,
            interval: 0,
            textStyle: {
              color: "#2b2b2b",
              fontSize: 14,
            },
          },
        },
      ],
      yAxis: {
        splitLine: { show: false },
        axisTick: {
          lineStyle: {
            color: "#ddd",
          },
        },
        axisLine: {
          lineStyle: {
            color: "#ddd",
          },
        },
        axisLabel: {
          textStyle: {
            color: "#2b2b2b",
          },
        },
      },
      animationEasing: "cubicOut",
      animationDuration: 100,
      animationDurationUpdate: 2000,
      series: [
        {
          type: "pictorialBar",
          name: "all",
          id: "paper",
          hoverAnimation: true,
          label: {
            normal: {
              show: true,
              position: "top",
              formatter: "{c} km",
              textStyle: {
                fontSize: 16,
                color: "#2b2b2b",
              },
            },
          },
          symbol: imageSymbols.paper,
          symbolSize: ["70%", 50],
          symbolMargin: "-25%",
          data: [
            {
              value: maxData,
              symbolRepeat: true,
            },
            {
              value: startData,
              symbolRepeat: true,
            },
            {
              value: startData,
              symbolBoundingData: startData,
              symbolRepeat: true,
            },
            {
              value: startData,
              symbolBoundingData: startData,
              symbolRepeat: 20,
            },
          ],
          markLine: {
            symbol: ["none", "none"],
            label: {
              normal: { show: false },
            },
            lineStyle: {
              normal: {
                color: "#308e87",
              },
            },
            data: [
              {
                yAxis: startData,
              },
            ],
          },
        },
        {
          name: "all",
          type: "pictorialBar",
          symbol: "circle",
          itemStyle: {
            normal: {
              color: "#ffffff",
            },
          },
          silent: true,
          symbolSize: ["150%", 50],
          symbolOffset: [0, 20],
          z: -10,
          data: [1, 1, 1, 1],
        },
      ],
    },
    function (echarts, chart) {
      setInterval(function () {
        var dynamicData = Math.round(
          Math.random() * (maxData - minData) + minData
        );

        chart.setOption({
          series: [
            {
              id: "paper",
              data: [
                {
                  value: maxData,
                  symbolRepeat: true,
                },
                {
                  value: dynamicData,
                  symbolRepeat: true,
                },
                {
                  value: dynamicData,
                  symbolBoundingData: dynamicData,
                  symbolRepeat: true,
                },
                {
                  value: dynamicData,
                  symbolBoundingData: dynamicData,
                  symbolRepeat: 20,
                },
              ],
              markLine: {
                data: [
                  {
                    yAxis: dynamicData,
                  },
                ],
              },
            },
          ],
        });
      }, 3000);
    }
  );

  // radar chart js
  require([
    "echarts",
    // 'echarts/chart/radar',
    // 'echarts/component/legend',
    // 'echarts/component/tooltip',
    // 'echarts/component/visualMap'
  ], function (echarts) {
    var chart = echarts.init(document.getElementById("echart-radar"));

    chart.setOption({
      title: {
        text: "Browser accounted for changes",
        subtext: "Purely fictitious",
        x: "right",
        y: "bottom",
      },
      tooltip: {
        trigger: "item",
        backgroundColor: "rgba(0,0,250,0.2)",
      },
      legend: {
        data: (function () {
          var list = [];
          for (var i = 1; i <= 28; i++) {
            list.push(i + 2000 + "");
          }
          return list;
        })(),
      },
      visualMap: {
        color: ["red", "yellow"],
      },
      radar: {
        indicator: [
          { text: "IE8-", max: 400 },
          { text: "IE9+", max: 400 },
          { text: "Safari", max: 400 },
          { text: "Firefox", max: 400 },
          { text: "Chrome", max: 400 },
        ],
      },
      series: (function () {
        var series = [];
        for (var i = 1; i <= 28; i++) {
          series.push({
            name: "Browser（Data is purely fictitious）",
            type: "radar",
            symbol: "none",
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 1,
                },
              },
            },
            emphasis: {
              areaStyle: {
                color: "rgba(0,250,0,0.3)",
              },
            },
            data: [
              {
                value: [
                  (40 - i) * 10,
                  (38 - i) * 4 + 60,
                  i * 5 + 10,
                  i * 9,
                  (i * i) / 2,
                ],
                name: i + 2000 + "",
              },
            ],
          });
        }
        return series;
      })(),
    });
  });

  // polar chart js
  require([
    "echarts",
    // 'echarts/chart/line',
    // 'echarts/component/legend',
    // 'echarts/component/polar',
    // 'echarts/component/tooltip',
    // 'echarts/component/markPoint',
    // 'echarts/component/markLine'
  ], function (echarts) {
    var chart = echarts.init(document.getElementById("main0"), null, {});

    var xAxisData = [];
    var data1 = [];
    var data2 = [];
    var data3 = [];

    for (var i = 0; i < 10; i++) {
      xAxisData.push("Category" + i);
      data1.push((Math.random() * 2 + 1).toFixed(3));
      data3.push((Math.random() + 0.5).toFixed(3));
      data2.push((Math.random() + 0.5).toFixed(3));
    }

    chart.setOption({
      legend: {
        data: ["line", "line2", "line3"],
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      polar: {},
      angleAxis: {
        // data: ['类目1', '类目2', '类目3', '类目4', '类目5',]
        data: xAxisData,
        startAngle: 30,
      },
      radiusAxis: {
        axisLine: {
          symbol: "arrow",
          symbolOffset: [20, -20],
        },
      },
      animationDuration: 5000,
      series: [
        {
          coordinateSystem: "polar",
          name: "line",
          stack: "all",
          type: "line",
          symbolSize: 10,
          label: {
            show: true,
          },
          itemStyle: {
            normal: {
              areaStyle: {},
            },
          },
          markPoint: {
            data: [
              {
                type: "max",
              },
            ],
          },
          data: data1,
        },
        {
          coordinateSystem: "polar",
          name: "line2",
          stack: "all",
          type: "line",
          symbolSize: 10,
          label: {
            show: true,
          },
          itemStyle: {
            normal: {
              areaStyle: {},
            },
          },
          markLine: {
            data: [
              [
                {
                  type: "max",
                },
                {
                  type: "min",
                },
              ],
            ],
          },
          data: data2,
        },
        {
          coordinateSystem: "polar",
          name: "line3",
          stack: "all",
          type: "line",
          symbolSize: 10,
          label: {
            show: true,
          },
          itemStyle: {
            normal: {
              areaStyle: {},
            },
          },
          data: data3,
        },
      ],
    });
  });

  // pie rich chart js
  require(["echarts" /*, 'map/js/china' */], function (echarts) {
    var option;
    // $.getJSON('./data/nutrients.json', function (data) {});
    var colorList = [
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "rgba(51,192,205,0.01)", // 0% 处的颜色
          },
          {
            offset: 1,
            color: "rgba(51,192,205,0.57)", // 100% 处的颜色
          },
        ],
        globalCoord: false, // 缺省为 false
      },
      {
        type: "linear",
        x: 1,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: "rgba(115,172,255,0.02)", // 0% 处的颜色
          },
          {
            offset: 1,
            color: "rgba(115,172,255,0.67)", // 100% 处的颜色
          },
        ],
        globalCoord: false, // 缺省为 false
      },
      {
        type: "linear",
        x: 1,
        y: 0,
        x2: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: "rgba(158,135,255,0.02)", // 0% 处的颜色
          },
          {
            offset: 1,
            color: "rgba(158,135,255,0.57)", // 100% 处的颜色
          },
        ],
        globalCoord: false, // 缺省为 false
      },
      {
        type: "linear",
        x: 0,
        y: 1,
        x2: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: "rgba(252,75,75,0.01)", // 0% 处的颜色
          },
          {
            offset: 1,
            color: "rgba(252,75,75,0.57)", // 100% 处的颜色
          },
        ],
        globalCoord: false, // 缺省为 false
      },
      {
        type: "linear",
        x: 1,
        y: 1,
        x2: 1,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: "rgba(253,138,106,0.01)", // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#FDB36ac2", // 100% 处的颜色
          },
        ],
        globalCoord: false, // 缺省为 false
      },
      {
        type: "linear",
        x: 0,
        y: 0,
        x2: 1,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: "rgba(254,206,67,0.12)", // 0% 处的颜色
          },
          {
            offset: 1,
            color: "#FECE4391", // 100% 处的颜色
          },
        ],
        globalCoord: false, // 缺省为 false
      },
    ];
    var colorLine = [
      "#33C0CD",
      "#73ACFF",
      "#308e87",
      "#f39159",
      "#FDB36A",
      "#FECE43",
    ];

    function getRich() {
      let result = {};
      colorLine.forEach((v, i) => {
        result[`hr${i}`] = {
          backgroundColor: colorLine[i],
          borderRadius: 3,
          width: 3,
          height: 3,
          padding: [0, 3, 3, -12],
        };
        result[`a${i}`] = {
          padding: [-20, -60, 0, -20],
          color: colorLine[i],
          fontSize: 12,
        };
      });
      return result;
    }
    let data = [
      {
        name: "Beijing",
        value: 25,
      },
      {
        name: "Shanghai",
        value: 20,
      },
      {
        name: "Guangzhou",
        value: 18,
      },
      {
        name: "Shenzhen",
        value: 15,
      },
      {
        name: "unknown",
        value: 13,
      },
      {
        name: "overseas",
        value: 9,
      },
    ].sort((a, b) => {
      return b.value - a.value;
    });
    data.forEach((v, i) => {
      v.labelLine = {
        lineStyle: {
          width: 1,
          color: colorLine[i],
        },
      };
    });
    option = {
      series: [
        {
          type: "pie",
          radius: "60%",
          center: ["50%", "50%"],
          clockwise: true,
          avoidLabelOverlap: true,
          label: {
            show: true,
            position: "outside",
            formatter: function (params) {
              const name = params.name;
              const percent = params.percent + "%";
              const index = params.dataIndex;
              return [`{a${index}|${name}：${percent}}`, `{hr${index}|}`].join(
                "\n"
              );
            },
            rich: getRich(),
          },
          itemStyle: {
            normal: {
              color: function (params) {
                return colorList[params.dataIndex];
              },
            },
          },
          data,
          roseType: "radius",
        },
      ],
    };

    var chart = testHelper.create(echarts, "echart-pierich", {
      option: option,
      // height: 300,
      // buttons: [{text: 'btn-txt', onclick: function () {}}],
      // recordCanvas: true,
    });
  });

  // pictorial single chart js
  function makeChart(id, option, cb) {
    require([
      "echarts",
      // 'echarts/chart/pictorialBar',
      // 'echarts/chart/bar',
      // 'echarts/chart/line',
      // 'echarts/chart/scatter',
      // 'echarts/component/grid',
      // 'echarts/component/legend',
      // 'echarts/component/markLine',
      // 'echarts/component/tooltip',
      // 'echarts/component/dataZoom'
    ], function (echarts) {
      if (typeof option === "function") {
        option = option(echarts);
      }

      var main = document.getElementById(id);
      if (main) {
        var chartMain = document.createElement("div");
        chartMain.style.cssText = "height:100%";
        main.appendChild(chartMain);
        var chart = echarts.init(chartMain);
        chart.setOption(option);

        window.addEventListener("resize", chart.resize);

        cb && cb(echarts, chart);
      }
    });
  }

  makeChart("dotted", function (echarts) {
    var bgColor = "#f8f8f8";
    var category = [];
    var count = 20;
    var dottedBase = +new Date();
    var lineData = [];
    var barData = [];

    for (var i = 0; i < count; i++) {
      var date = new Date(dottedBase + 3600 * 24);
      category.push(
        [date.getFullYear(), date.getMonth() + 1, date.getDate()].join("-")
      );
      var b = Math.random() * 200;
      var d = Math.random() * 200;
      barData.push(b);
      lineData.push(d + b);
    }

    return {
      backgroundColor: bgColor,
      tooltip: {},
      legend: {
        data: ["line", "bar"],
        textStyle: {
          color: "#ccc",
        },
      },
      xAxis: {
        data: category,
        axisLine: {
          lineStyle: {
            color: "#ccc",
          },
        },
      },
      yAxis: {
        splitLine: { show: false },
        axisLine: {
          lineStyle: {
            color: "#ccc",
          },
        },
      },
      series: [
        {
          name: "line",
          type: "line",
          smooth: true,
          showAllSymbol: true,
          symbol: "emptyCircle",
          symbolSize: 15,
          data: lineData,
        },
        {
          name: "bar",
          type: "bar",
          barWidth: 10,
          itemStyle: {
            normal: {
              barBorderRadius: 5,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: "#14c8d4" },
                  { offset: 1, color: "#308e87" },
                ],
              },
            },
          },
          data: barData,
        },
        {
          name: "line",
          type: "bar",
          barWidth: 10,
          barGap: "-100%",
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(48, 142, 135,0.5)" },
                { offset: 0.2, color: "rgba(48, 142, 135,0.2)" },
                { offset: 1, color: "rgba(48, 142, 135,0)" },
              ]),
            },
          },
          z: -12,
          data: lineData,
        },
        {
          name: "dotted",
          type: "pictorialBar",
          symbol: "rect",
          itemStyle: {
            normal: {
              color: bgColor,
            },
          },
          symbolRepeat: true,
          symbolSize: [12, 4],
          symbolMargin: 1,
          z: -10,
          data: lineData,
        },
      ],
    };
  });
})(jQuery);
